PROGRAM SPTOGP
!======================================================================!
! 程序名：SPTOGP
! 功能  ：将 GRIB 格式的谱系数（spectral）数据转换为格点（grid-point）数据
! 说明  ：支持串行与 MPI 并行，支持多种网格（高斯网格、线性网格、三次网格等）
!         通过命令行参数灵活控制输入输出、分辨率、网格类型、字段筛选等
!======================================================================!

USE PARKIND1  ,ONLY : JPIM     ,JPRB, JPRD   ! ECMWF 精度定义模块：JPIM=整型精度, JPRB=实型精度, JPRD=双精度

USE MPL_MPIF, ONLY : MPI_COMM_WORLD          ! MPI 通信域
USE MPL_MODULE                           ! ECMWF MPL 并行库封装
USE GRIB_API                             ! GRIB API 库，用于读写 GRIB 消息

IMPLICIT NONE

! 定义 GRIB 各节的标志位（应在 GRIB_API 模块中定义，此处为兼容旧版本）
INTEGER, PARAMETER :: GRIB_SECTION_PRODUCT =  1   ! 产品定义节
INTEGER, PARAMETER :: GRIB_SECTION_GRID    =  2   ! 网格描述节
INTEGER, PARAMETER :: GRIB_SECTION_LOCAL   =  4   ! 本地节
INTEGER, PARAMETER :: GRIB_SECTION_DATA    =  8   ! 数据节
INTEGER, PARAMETER :: GRIB_SECTION_BITMAP  = 16   ! 位图节

CHARACTER OPTIONS*34                      ! 命令行选项定义字符串
DATA OPTIONS/'h;l;q:s:S:G:t:f:p:P:n;N:b:B;V;D;X;'/
CHARACTER ARG*127, CTYPEG*1, OPTLET       ! ARG：命令行参数；CTYPEG：网格类型；OPTLET：当前选项字母
CHARACTER*127 COUTSF, COUTGPF, CINSF, CFIELD, CRTABLE, CPROC, CBITS, CQ  ! 文件名、路径、参数字符串
CHARACTER*127 CGRIDTYPE, CNDGL, CSAMPLESFILE  ! 网格类型、分辨率描述等
CHARACTER*1 CEDITION                      ! GRIB 版本号字符

INTEGER(KIND=JPIM),PARAMETER :: JPMLAT=16000   ! 最大纬度圈数（用于读取分辨率表）
INTEGER(KIND=JPIM),PARAMETER :: JPMAXFLD=31    ! 每批处理最大字段数（并行通信缓冲限制）

INTEGER(KIND=JPIM) :: ISTACK, getstackusage, OPTVAL  ! ISTACK：栈使用量；OPTVAL：getopt返回值
REAL(KIND=JPRB)    :: Z, RPI                        ! 临时变量、圆周率
INTEGER(KIND=JPIM) :: NRGRI(JPMLAT)                 ! 分辨率表中每纬度圈格点数

INTEGER(KIND=JPIM) :: NERR,NINTLEN,NPROC,NLIN,INSF,ILENG,IOUTLEN,IWORD,NSMAX,NDGL,NDLON,NQ
INTEGER(KIND=JPIM) :: ITABLE,NOUT,NSPEC2,NGPTOT,NGPTOTG,IMAXFLD,IFLD,ICODE,IOUTSF,JROC
INTEGER(KIND=JPIM) :: IERR,IMLEN,ISENDR,ITAGR,ITAG,NSPEC2G,IRET,NTYPE,I,ISAMPLE,IGRIBOUT
INTEGER(KIND=JPIM) :: IOUTGPF,JFLD
INTEGER(KIND=JPIM), DIMENSION(JPMAXFLD+1) :: IPARAM,IGRIB,IEDITION  ! 字段参数ID、GRIB句柄、GRIB版本
INTEGER(KIND=JPIM) ,ALLOCATABLE :: NLOEN(:),ITO(:),NPRCIDS(:)        ! NLOEN：每纬度圈格点数；ITO：通信映射；NPRCIDS：进程ID表
INTEGER(KIND=JPIM) :: GETOPT,MYPROC,JJ                          ! GETOPT：命令行解析函数；MYPROC：当前进程号
INTEGER   :: IPROC,IPROC_OLD,ICOMM,ICOMM_OLD, IBITS            ! IPROC：用户指定进程数；IBITS：GRIB压缩位深
REAL(KIND=JPRB)    :: ZT,TIMEF,ZCODE                           ! 计时器、时间函数、参数字段代码
REAL(KIND=JPRB),ALLOCATABLE :: ZSPECG(:,:),ZSPEC(:,:),ZFPDAT(:)   ! 谱系数数组（全局/局部分片）
REAL(KIND=JPRB),ALLOCATABLE :: ZG(:,:,:),ZGG(:,:)               ! 格点数据数组
REAL(KIND=JPRD),ALLOCATABLE :: RMU(:)                           ! 高斯权重（纬度方向）
LOGICAL :: LTRUNC,LDONE,LSCAL,LSTDEV,LVAR,LGRIBEX,LLFLT         ! 逻辑开关：截断、完成、串行、标准差、方差、GRIBEX模式、快速勒让德
integer ia
NAMELIST/NAMRGRI/ NRGRI                                        ! 分辨率表namelist

!     ------------------------------------------------------------------

#include "setup_trans0.h"
#include "setup_trans.h"
#include "inv_trans.h"
#include "dist_spec.h"
#include "gath_grid.h"
#include "trans_inq.h"
#include "specnorm.h"

! Initializations
NERR = 0
NOUT = 6
IMAXFLD = JPMAXFLD

ia=0

! Set defaults for options

CINSF   = ' '
COUTSF  = ' '
COUTGPF = ' '
CFIELD  = ' '
CTYPEG  = 'r'
IPROC   = 1
LSCAL   = .FALSE.
LSTDEV    = .FALSE.
LVAR    = .FALSE.
LGRIBEX = .FALSE.
LTRUNC  = .TRUE.
LLFLT   = .FALSE.
NLIN    = 0
NQ      = 0
NDGL    = 0
IBITS   = 16
CRTABLE = '/home/<USER>/data/ifs'

! Crack options

DO
  OPTVAL = GETOPT(OPTIONS,ARG)
  IF(OPTVAL <= 0) EXIT
  OPTLET=CHAR(OPTVAL)
  IF(OPTVAL <= 0) EXIT
  IF(OPTLET == 'h') THEN
    CALL USE_SPTOGP
    STOP
  ELSEIF(OPTLET == 'l') THEN
    NLIN = 1
    WRITE(NOUT,*) 'GRID changed to linear grid'
  ELSEIF(OPTLET == 'q') THEN
    CQ = ARG
    READ(CQ,*) NQ
    WRITE(NOUT,*) 'GRID changed to cubic grid'
  ELSEIF(OPTLET == 's')  THEN
    CINSF = ARG
  ELSEIF(OPTLET == 'S') THEN
    COUTSF = ARG
  ELSEIF(OPTLET == 'G') THEN
    COUTGPF = ARG
  ELSEIF(OPTLET == 't') THEN
    CTYPEG = ARG
  ELSEIF(OPTLET == 'f') THEN
    CFIELD = ARG
  ELSEIF(OPTLET == 'p') THEN
    CRTABLE = ARG
  ELSEIF(OPTLET == 'P') THEN
    CPROC = ARG
    READ(CPROC,*) IPROC
  ELSEIF(OPTLET == 'n') THEN
    LTRUNC = .FALSE.
  ELSEIF(OPTLET == 'N') THEN
    CNDGL = ARG
    READ(CNDGL,*) NDGL
    NDGL=NDGL*2
  ELSEIF(OPTLET == 'b') THEN
    CBITS = ARG
    READ(CBITS,*)IBITS
    WRITE(NOUT,*)'number of bits per number in grib coding changed to:',IBITS
  ELSEIF(OPTLET == 'B') THEN
    LLFLT=.true.
    WRITE(NOUT,*)'FAST Legendre transform on'
  ELSEIF(OPTLET == 'V') THEN
    LVAR = .TRUE.
  ELSEIF(OPTLET == 'D') THEN
    LSTDEV = .TRUE.
  ELSEIF(OPTLET == 'X') THEN
    LGRIBEX = .TRUE.
  ELSE
    PRINT *, ' ILLEGAL OPTION: -',OPTLET
    CALL USE_SPTOGP
    CALL ABOR1('SPTOGP:ILLEGAL COMMAND OPTION')    
  ENDIF
ENDDO

IF (IPROC == 1) LSCAL=.true.  !-- serial binary ---

! Message passing setup
! Participating processors limited by -P option

!--------------------------
 ZT=TIMEF()
 CALL MPL_INIT()

 NPROC=MPL_NPROC()
 MYPROC = MPL_MYRANK()
 IPROC_OLD=NPROC
 IF(IPROC_OLD > IPROC) THEN
   CALL MPL_LOCOMM_CREATE(IPROC,ICOMM)
   IF(MYPROC > IPROC) THEN
     CALL MPL_BARRIER()
     CALL MPL_END()
     STOP
   ENDIF
   CALL MPL_SETDFLT_COMM(ICOMM,ICOMM_OLD)
   NPROC=IPROC
 ENDIF
!-------------------------

ALLOCATE(NPRCIDS(NPROC))
DO JJ=1,NPROC
  NPRCIDS(JJ) = JJ
ENDDO
ITAG = 191919

IF(MYPROC == 1) THEN
  IF(CINSF == ' ') THEN
    PRINT *,' INPUT FILE NAME HAS NOT BEEN SUPPLIED'
    PRINT *,' FILE fort.11 WILL BE USED AS INPUT'
    CINSF = 'fort.11'
  ENDIF
  IF(COUTSF == ' ') THEN
    PRINT *,' OUTPUT SPECTRAL FILE NAME HAS NOT BEEN SUPPLIED'
    PRINT *,' FILE fort.10 WILL BE USED FOR SPECTRAL OUTPUT'
    COUTSF = 'fort.10'
  ENDIF
  IF(COUTGPF == ' ') THEN
    PRINT *,' OUTPUT GRID-POINT FILE NAME HAS NOT BEEN SUPPLIED'
    PRINT *,' FILE fort.12 WILL BE USED FOR GRID-POINT OUTPUT'
    COUTGPF = 'fort.12'
  ENDIF
ENDIF

IF(CTYPEG == 'r') THEN
  NTYPE = 1
ELSEIF(CTYPEG == 'f') THEN
  NTYPE = 0
ELSE
  WRITE(NERR,*) ' WRONG TYPE OF GRID: ',CTYPEG,'  It should be',' either f or r'
  CALL ABOR1('SPTOGP:WRONG TYPE OF GRID')
ENDIF

ICODE = 0

IF(CFIELD /= ' ') THEN
  READ(CFIELD,'(F7.0)') ZCODE

  ! Handle possible param.table notation
  ICODE = FLOOR(ZCODE)
  ITABLE = NINT(1000*(ZCODE-ICODE))
  IF (ITABLE /= 128) ICODE = 1000*ITABLE + ICODE
ENDIF

CALL GRIB_GRIBEX_MODE_ON()

! Find spectral resolution

CALL GRIB_OPEN_FILE(INSF,CINSF,'R',IRET)
IF(IRET /= GRIB_SUCCESS) THEN
  WRITE(NERR,*) ' ERROR OPENING FILE INPUT SPECTRAL FILE',CINSF,IRET
  CALL ABOR1('SPTOGP: ERROR OPENING FILE INPUT SPECTRAL FILE')
ENDIF
CALL GRIB_NEW_FROM_FILE(INSF,IGRIB(1),IRET)
IF(IRET /= GRIB_SUCCESS) THEN
  WRITE(NERR,*)' ERROR IN FILE ',CINSF,' : NO INFORMATION'
  CALL ABOR1('SPTOGP: ERROR GRIB_NEW_FROM_FILE')
ENDIF
CALL GRIB_GET(IGRIB(1),'gridType',CGRIDTYPE,IRET)
IF(CGRIDTYPE /= 'sh') THEN
  WRITE(NERR,*)' INPUT DATA NOT IN SPECTRAL FORM'
  CALL ABOR1('SPTOGP:INPUT DATA NOT IN SPECTRAL FORM')
ENDIF

CALL GRIB_GET(IGRIB(1),'pentagonalResolutionParameterJ',NSMAX)
IF(NSMAX > 511) IMAXFLD=5
IF(NSMAX > 800) IMAXFLD=1

! Decide gridpoint resolution

IF (NDGL == 0) THEN
  CALL SETNDGL
ELSE
  CALL CHECK_NDGL
ENDIF

PRINT *, 'Number of meridional points: ',NDGL

ALLOCATE(NLOEN(NDGL))

IF(NTYPE == 0) THEN
  NLOEN(:) = 2*NDGL
ELSEIF(NQ == 1.AND.NTYPE == 1) THEN
! cubic grid
  ITABLE = INDEX(CRTABLE,' ')
  IF(NSMAX < 1000) THEN
    WRITE(CRTABLE(ITABLE:ITABLE+11),'(A,I3.3)') '/rtable_3',NSMAX
  ELSE
    WRITE(CRTABLE(ITABLE:ITABLE+12),'(A,I4.4)') '/rtable_3',NSMAX
  ENDIF
ELSEIF(NQ == 2.AND.NTYPE == 1) THEN
! cubic grid + Collignon
  ITABLE = INDEX(CRTABLE,' ')
  IF(NSMAX < 1000) THEN
    WRITE(CRTABLE(ITABLE:ITABLE+11),'(A,I3.3)') '/rtable_4',NSMAX
  ELSE
!    IF (NSMAX == 15999) THEN
!     WRITE(CRTABLE(ITABLE:ITABLE+12),'(A,I4.4)') '/rtable_4',7999
!    ELSE
     WRITE(CRTABLE(ITABLE:ITABLE+12),'(A,I4.4)') '/rtable_4',NSMAX
!    ENDIF
  ENDIF
! PRINT *, 'NQ=2, NTYPE=1,CRTABLE, NSMAX: ',CRTABLE, NSMAX
ELSEIF(NQ == 3.AND.NTYPE == 1) THEN
! cubic grid + Collignon
  ITABLE = INDEX(CRTABLE,' ')
  IF(NSMAX < 1000) THEN
    WRITE(CRTABLE(ITABLE:ITABLE+11),'(A,I3.3)') '/rtable_5',NSMAX
  ELSE
    WRITE(CRTABLE(ITABLE:ITABLE+12),'(A,I4.4)') '/rtable_5',NSMAX
  ENDIF
! PRINT *, 'NQ=2, NTYPE=1, NSMAX: ',NSMAX  
ELSEIF(NLIN == 0.AND.NTYPE == 1) THEN
! quadratic grid
  ITABLE = INDEX(CRTABLE,' ')
  IF(NSMAX < 1000) THEN
    WRITE(CRTABLE(ITABLE:ITABLE+11),'(A,I3.3)') '/rtable_2',NSMAX
  ELSE
    WRITE(CRTABLE(ITABLE:ITABLE+12),'(A,I4.4)') '/rtable_2',NSMAX
  ENDIF
ELSEIF(NLIN == 1.AND.NTYPE == 1) THEN
  ITABLE = INDEX(CRTABLE,' ')
  IF(NSMAX < 1000) THEN
    WRITE(CRTABLE(ITABLE:ITABLE+12),'(A,I3.3)') '/rtablel_2',NSMAX
  ELSE
    WRITE(CRTABLE(ITABLE:ITABLE+13),'(A,I4.4)') '/rtablel_2',NSMAX
  ENDIF
ENDIF
IF(NTYPE == 1) THEN
  IF(MYPROC == 1) THEN
    PRINT *,' TABLE USED FOR REDUCED GRID:'
    PRINT *,CRTABLE
  ENDIF
  OPEN(15,FILE=CRTABLE,FORM='FORMATTED',ACTION='READ')
  READ(15,NAMRGRI)
  NLOEN(:) = NRGRI(1:NDGL)
  CLOSE(15)
ENDIF

CALL GRIB_RELEASE(IGRIB(1))
CALL GRIB_CLOSE_FILE(INSF)

! Prepare for transforms

ALLOCATE(RMU(NDGL))
CALL SETUP_TRANS0(KOUT=NOUT,KERR=NERR,KPRINTLEV=0,KMAX_RESOL=1,&
&                 KPRGPNS=NPROC,KPRGPEW=1,KPRTRW=NPROC,LDMPOFF=LSCAL)
CALL SETUP_TRANS(KSMAX=NSMAX,KDGL=NDGL,KLOEN=NLOEN,LDSPLIT=.FALSE.,&
&                LDUSEFFTW=.TRUE.,&
&                LDUSEFLT=LLFLT,LDUSERPNM=.TRUE.,LDKEEPRPNM=.FALSE.)
!
CALL TRANS_INQ(KSPEC2=NSPEC2,KSPEC2G=NSPEC2G,KGPTOT=NGPTOT,KGPTOTG=NGPTOTG)
CALL TRANS_INQ(PMU=RMU)

ALLOCATE(ZSPEC(IMAXFLD,NSPEC2))
ALLOCATE(ZG(NGPTOT,IMAXFLD,1))
ALLOCATE(ITO(IMAXFLD))

! Open files

IF(MYPROC == 1) THEN
  ALLOCATE(ZFPDAT(NSPEC2G))
  ALLOCATE(ZGG(NGPTOTG,IMAXFLD))
  ALLOCATE(ZSPECG(IMAXFLD,NSPEC2G))
  CALL GRIB_OPEN_FILE(INSF,CINSF,'R',IRET)
  IF(IRET /= GRIB_SUCCESS) THEN
    WRITE(NERR,*) ' ERROR OPENING FILE INPUT SPECTRAL FILE',CINSF,IRET
    CALL ABOR1(' SPTOGP:ERROR OPENING FILE INPUT SPECTRAL FILE')
  ENDIF
  CALL GRIB_OPEN_FILE(IOUTGPF,COUTGPF,'W',IRET)
  IF(IRET /= GRIB_SUCCESS) THEN
    WRITE(NERR,*) ' ERROR OPENING OUTPUT GRIDPOINT FILE',COUTGPF,IRET
    CALL ABOR1('SPTOGP:ERROR OPENING OUTPUT GRIDPOINT FILE')
  ENDIF
  IF(ICODE /= 0) THEN
    CALL GRIB_OPEN_FILE(IOUTSF,COUTSF,'W',IRET)
    IF(IRET /= GRIB_SUCCESS) THEN
      WRITE(NERR,*) ' ERROR OPENING OUTPUT SPECTRAL FILE',COUTSF,IRET
      CALL ABOR1(' SPTOGP:ERROR OPENING OUTPUT SPECTRAL FILE')
    ENDIF
  ENDIF
ELSE
  ALLOCATE(ZGG(NGPTOT,IMAXFLD))
  ALLOCATE(ZSPECG(IMAXFLD,NSPEC2))
ENDIF


! Spectral to gridpoint transform

ITO(:) = 1
IFLD = 0
LDONE = .FALSE.

! Inititialize GRIB_API handles to zero
IGRIB(:) = 0
ISAMPLE = 0
IGRIBOUT = 0

DO
  IF(MYPROC == 1) THEN

    ! Read and decode spectral field
    CALL GRIB_NEW_FROM_FILE(INSF,IGRIB(IFLD+1),IRET)
    IF(IRET == GRIB_END_OF_FILE) LDONE = .TRUE.

    IF(.NOT. LDONE) THEN
      IF(IRET /= GRIB_SUCCESS) THEN
        WRITE(NERR,*) ' ERROR GRIB_NEW_FROM_FILE',IRET
        CALL ABOR1('SPTOGP:ERROR GRIB_NEW_FROM_FILE')
      ENDIF

      CALL GRIB_GET(IGRIB(IFLD+1),'edition',IEDITION(IFLD+1),IRET)
      IF(IRET /= GRIB_SUCCESS) THEN
        WRITE(NERR,*)' ERROR GRIB_GET edition'
        CALL ABOR1('SPTOGP:ERROR GRIB_GET edition')
      ENDIF

      CALL GRIB_GET(IGRIB(IFLD+1),'paramId',IPARAM(IFLD+1),IRET)
      IF(IRET /= GRIB_SUCCESS) THEN
        WRITE(NERR,*)' ERROR GRIB_GET paramId'
        CALL ABOR1('SPTOGP:ERROR GRIB_GET paramId')
      ENDIF

      ! Write out "skipped field" to output spectral file
      IF(IPARAM(IFLD+1) /= ICODE.AND.ICODE /= 0) THEN
        PRINT *,' FIELD ',IPARAM(IFLD+1),' NOT TRANSFORMED'
        CALL GRIB_CLONE(IGRIB(IFLD+1),IGRIBOUT,IRET)
        IF(IRET /= GRIB_SUCCESS) THEN
          WRITE(NERR,*)'SPTOGP:ERROR GRIB_CLONE'
          CALL ABOR1('SPTOGP:ERROR GRIB_CLONE')
        ENDIF
        CALL GRIB_WRITE(IGRIBOUT,IOUTSF,IRET)
        IF(IRET /= GRIB_SUCCESS) THEN
          WRITE(NERR,*)'SPTOGP:ERROR GRIB_WRITE'
          CALL ABOR1('SPTOGP:ERROR GRIB_WRITE')
        ENDIF
        CALL GRIB_RELEASE(IGRIBOUT)
        CYCLE
      ENDIF

      CALL GRIB_GET(IGRIB(IFLD+1),'values',ZFPDAT,IRET)
      IF(IRET /= GRIB_SUCCESS) THEN
        WRITE(NERR,*)' ERROR GRIB_GET values ',IRET
        CALL ABOR1('SPTOGP:ERROR GRIB_GET values')
      ENDIF

      IFLD = IFLD+1
      ZSPECG(IFLD,:)=ZFPDAT(:)
    ENDIF

    IF(LDONE .OR. IFLD == IMAXFLD) THEN

      ! Send number of fields in this batch to other processors
      IF(NPROC > 1) THEN
        DO JROC=2,NPROC
          CALL MPL_SEND(IFLD,KDEST=NPRCIDS(JROC),KTAG=ITAG)
        ENDDO
      ENDIF
    ELSE
      CYCLE
    ENDIF

  ELSE

    ! Recieve number of fields in this batch
    CALL MPL_RECV(IFLD,KSOURCE=NPRCIDS(1),KTAG=ITAG)

  ENDIF

  IF(NPROC > 1) THEN
    CALL MPL_BROADCAST(IPARAM(1:IFLD),KROOT=1,KTAG=ITAG, &
             &   CDSTRING='SPTOGP:')
  ENDIF
  IF(IFLD == 0 ) EXIT
  ! Syncronize processors
  IF(NPROC > 1) THEN
    CALL MPL_BARRIER()
  ENDIF
  ! Distribute spectral fields to processors
  CALL DIST_SPEC(PSPECG=ZSPECG,KFDISTG=IFLD,KFROM=ITO,PSPEC=ZSPEC)
  ! Spectral transform
  CALL INV_TRANS(PSPSCALAR=ZSPEC(1:IFLD,:),PGP=ZG)
  ! Limit fields
  IF(LTRUNC.OR.LSTDEV.OR.LVAR) THEN
    CALL MAXMINL
  ENDIF
  ! Gather gridpoint fields to processor 1
  CALL GATH_GRID(PGPG=ZGG,KFGATHG=IFLD,KTO=ITO,PGP=ZG)
  IF(MYPROC == 1) THEN
    DO JFLD=1,IFLD
      IF (NTYPE == 0) THEN
        CSAMPLESFILE = 'regular_gg_pl_grib'
      ELSE
        CSAMPLESFILE = 'reduced_gg_pl_grib'
      ENDIF
      WRITE(CEDITION,'(I1)') IEDITION(JFLD)
      CSAMPLESFILE = TRIM(CSAMPLESFILE)//CEDITION
      CALL GRIB_NEW_FROM_SAMPLES(ISAMPLE,CSAMPLESFILE,IRET)
      IF(IRET /= GRIB_SUCCESS) THEN
        WRITE(NERR,*)' ERROR GRIB_NEW_FROM_SAMPLES ',CSAMPLESFILE
        CALL ABOR1('SPTOGP:ERROR GRIB_NEW_FROM_SAMPLES')
      ENDIF

      RPI = 2.0_JPRB*ASIN(1.0_JPRB)
      NDLON = 2*NDGL
      IF (NTYPE == 0) THEN
        CALL GRIB_SET(ISAMPLE,'Ni',NDLON)
        CALL GRIB_SET(ISAMPLE,'iDirectionIncrementInDegrees', 360._JPRB/NDLON)
      ELSE
        CALL GRIB_SET_MISSING(ISAMPLE,'Ni')
        CALL GRIB_SET_MISSING(ISAMPLE,'iDirectionIncrement')
        CALL GRIB_SET(ISAMPLE,'pl',NLOEN(1:NDGL))
      ENDIF
      CALL GRIB_SET(ISAMPLE,'Nj',NDGL)
      CALL GRIB_SET(ISAMPLE,'N', NDGL/2)

      IF (.NOT. LGRIBEX) THEN
        CALL GRIB_SET(ISAMPLE,'latitudeOfFirstGridPointInDegrees', &
                    & 180._JPRB/RPI*ASIN(RMU(1)))
        CALL GRIB_SET(ISAMPLE,'latitudeOfLastGridPointInDegrees', &
                    & 180._JPRB/RPI*ASIN(RMU(NDGL)))
        CALL GRIB_SET(ISAMPLE,'longitudeOfLastGridPointInDegrees', &
                    & (360._JPRB*(NDLON-1))/(NDLON))
      ELSE
        CALL GRIB_SET(ISAMPLE,'latitudeOfFirstGridPointInDegrees', &
                    & INT(180._JPRB/RPI*1000._JPRB*ASIN(RMU(1)))/1000._JPRB)
        CALL GRIB_SET(ISAMPLE,'latitudeOfLastGridPointInDegrees', &
                    & INT(180._JPRB/RPI*1000._JPRB*ASIN(RMU(NDGL)))/1000._JPRB)
        CALL GRIB_SET(ISAMPLE,'longitudeOfLastGridPointInDegrees', &
                    & INT((360.0_JPRB*1000*(NDLON-1))/(NDLON))/1000._JPRB)
      ENDIF

      CALL GRIB_SET(ISAMPLE,'longitudeOfFirstGridPointInDegrees', 0.0_JPRB)
      CALL GRIB_SET(ISAMPLE,'resolutionAndComponentFlags',0)
      CALL GRIB_SET(ISAMPLE,'bitsPerValue',IBITS)

      CALL GRIB_UTIL_SECTIONS_COPY(IGRIB(JFLD),ISAMPLE, &
            & GRIB_SECTION_PRODUCT+GRIB_SECTION_LOCAL,IGRIBOUT,IRET)
      IF(IRET /= GRIB_SUCCESS) THEN
        WRITE(NERR,*)' ERROR GRIB_UTIL_SECTIONS_COPY ',IRET
        CALL ABOR1('SPTOGP:ERROR GRIB_UTIL_SECTIIONS_COPY')
      ENDIF

      CALL GRIB_SET(IGRIBOUT,'values',ZGG(1:NGPTOTG,JFLD),IRET)
      IF(IRET /= GRIB_SUCCESS) THEN
        WRITE(NERR,*)' ERROR GRIB_SET values'
        CALL ABOR1('SPTOGP:ERROR GRIB_SET values')
      ENDIF

      CALL GRIB_WRITE(IGRIBOUT,IOUTGPF,IRET)
      IF(IRET /= GRIB_SUCCESS) THEN
        WRITE(NERR,*)' ERROR GRIB_WRITE IOUTGPF'
        CALL ABOR1('SPTOGP:ERROR GRIB_WRITE IOUTGPF')
      ENDIF

      CALL GRIB_RELEASE(ISAMPLE)
      CALL GRIB_RELEASE(IGRIB(JFLD))
      CALL GRIB_RELEASE(IGRIBOUT)
    ENDDO
  ENDIF
  ! Syncronize processors
  IF(NPROC > 1) THEN
    CALL MPL_BARRIER()
  ENDIF
  IFLD = 0
ENDDO

IF (MYPROC == 1) THEN
  CALL GRIB_CLOSE_FILE(INSF)
  IF(ICODE /= 0) CALL GRIB_CLOSE_FILE(IOUTSF)
  CALL GRIB_CLOSE_FILE(IOUTGPF)
ENDIF

!           gather stack usage statistics
ISTACK = GETSTACKUSAGE()
IF(MYPROC == 1) THEN
  PRINT 9000, istack
  9000 FORMAT("Stack Utilisation Information",/,&
       &"=============================",//,&
       &"Node           Size(Bytes)",/,&
       &"====           ===========",//,&
       &"   1",11x,I10)

  DO I=2,NPROC
    CALL MPL_RECV(ISTACK,KSOURCE=NPRCIDS(I),KTAG=I, &
         & CDSTRING='SPTOGP:')
    PRINT '(I4,11X,I10)', I,ISTACK
  ENDDO
ELSE
  CALL MPL_SEND(ISTACK,KDEST=NPRCIDS(1),KTAG=MYPROC, &
             &   CDSTRING='SPTOGP:')
ENDIF

!Close down message passing

!--------------------------
 IF(IPROC_OLD > IPROC) THEN
     MPL_COMM_OML(1)=MPI_COMM_WORLD
     MPL_NUMPROC=IPROC_OLD
     CALL MPL_SETDFLT_COMM(ICOMM_OLD,ICOMM)
 ENDIF

 CALL MPL_BARRIER()
 CALL MPL_END()
 ZT=(TIMEF()-ZT)/1000.0_JPRB
 IF(MYPROC == 1) write(0,'(a,I3,a,I4,a,F9.2,a)') &
    & "SPTOGP, on",NPROC," out of",IPROC_OLD," procs, took",ZT," sec"
!--------------------------

STOP


CONTAINS  

!     ------------------------------------------------------------------

SUBROUTINE USE_SPTOGP

!   WRITE MESSAGE TO INFORM ABOUT CORRECT USE OF PROGRAM

WRITE(NERR,*) ' CORRECT OPTIONS AND ARGUMENTS FOR sptogp ARE :'
WRITE(NERR,*) '   -s      input filename (spectral) (def: fort.11)'
WRITE(NERR,*) '   -S      output spectral  filename (def: fort.10)'
WRITE(NERR,*) '   -G      output grid-point filename (def: fort.12)'
WRITE(NERR,*) '   -t         type of grid (r: reduced or f: full)',&
                     &' default reduced' 
WRITE(NERR,*) '   -l      if linear grid (default quadratic)'
WRITE(NERR,*) '   -q      1==_3 or 2==_4 if cubic grid (default quadratic)'
WRITE(NERR,*) '   -f      field code to convert to GP (def: 0)'
WRITE(NERR,*) '           if -f 0, all fields will be converted'
WRITE(NERR,*) '   -p      path for resolution tables ',&
                       &'default: /home/<USER>/data/ifs'
WRITE(NERR,*) '   -n      do not truncate output field','  (default truncate)'
WRITE(NERR,*) '   -V      enforce max/min values (i.e., for Variance fields) '
WRITE(NERR,*) '   -D      enforce max/min values (i.e., for StDev fields) '
WRITE(NERR,*) '   -N      Gaussian resolution (num. of lat. circles betw. Pole and Eq.'
WRITE(NERR,*) '   -b      Number of bits containing each packed value (default=16)'
WRITE(NERR,*) '   -B      Fast Legendre Transforms on'
WRITE(NERR,*) '   -X      try make output identical to old GRIBEX'
WRITE(NERR,*) '   -h      displays options and arguments'
WRITE(NERR,*) '   -P      number of processors to use (mandatory for parallel)'

END SUBROUTINE USE_SPTOGP

!     ------------------------------------------------------------------

SUBROUTINE SETNDGL

! Decide number of Gaussian latitudes given spectral truncation
! Only certain combinations of truncation/linear grid
! or quadratic grid are supported
 
IF(NLIN == 0 .AND. (NQ == 1.OR.NQ == 2)) THEN
  IF(NSMAX == 23) THEN
    NDGL = 160
  ELSEIF(NSMAX == 79) THEN
    NDGL = 160
  ELSEIF(NSMAX == 63) THEN
    NDGL = 128
  ELSEIF(NSMAX == 95) THEN
    NDGL = 192
  ELSEIF(NSMAX == 127) THEN
    NDGL = 256
  ELSEIF(NSMAX == 159) THEN
    NDGL = 320
  ELSEIF(NSMAX == 191) THEN
    NDGL = 384
  ELSEIF(NSMAX == 199) THEN
    NDGL = 400
  ELSEIF(NSMAX == 255) THEN
    NDGL = 512
  ELSEIF(NSMAX == 319) THEN
    NDGL = 640
  ELSEIF(NSMAX == 399) THEN
    NDGL = 800
  ELSEIF(NSMAX == 511) THEN
    NDGL = 1024
  ELSEIF(NSMAX == 639) THEN
    NDGL = 1280
  ELSEIF(NSMAX == 799) THEN
    NDGL = 1600
  ELSEIF (NSMAX == 911) THEN
    NDGL = 1824
  ELSEIF(NSMAX == 1023) THEN
    NDGL = 2048
  ELSEIF(NSMAX == 1279) THEN
    NDGL = 2560
  ELSEIF(NSMAX == 1599) THEN
    NDGL = 3200
  ELSEIF(NSMAX == 1999) THEN
    NDGL = 4000
  ELSEIF(NSMAX == 2559) THEN
    NDGL = 5120
  ELSEIF(NSMAX == 3999) THEN
    NDGL = 8000
  ELSEIF(NSMAX == 7999) THEN
    NDGL = 16000
  ELSEIF(NSMAX == 15999) THEN
    NDGL = 16000
  ELSE
    WRITE(NERR,*)' WRONG SPECTRAL RESOLUTION ',NSMAX,' CUBIC GRID'
    CALL ABOR1('SPTOGP:UNSUPPORTED SPECTRAL RESOLUTION - CUBIC GRID ')
  ENDIF
ELSEIF (NLIN == 0) THEN
  IF(NSMAX == 21) THEN
    NDGL = 32
  ELSEIF(NSMAX == 42) THEN
    NDGL = 64
  ELSEIF(NSMAX == 63) THEN
    NDGL = 96
  ELSEIF(NSMAX == 106) THEN
    NDGL = 160
  ELSEIF(NSMAX == 213) THEN
    NDGL = 320
  ELSEIF(NSMAX == 341) THEN
    NDGL = 512
  ELSEIF(NSMAX == 426) THEN
    NDGL = 640
  ELSEIF(NSMAX == 533) THEN
    NDGL = 800
  ELSEIF(NSMAX == 682) THEN
    NDGL = 1024
  ELSEIF(NSMAX == 853) THEN
    NDGL = 1280
  ELSEIF(NSMAX == 1364) THEN
    NDGL = 2048
  ELSEIF(NSMAX == 1706) THEN
    NDGL = 2560
  ELSE
    WRITE(NERR,*)' WRONG SPECTRAL RESOLUTION ',NSMAX,' QUAD. GRID'
    CALL ABOR1('SPTOGP:UNSUPPORTED SPECTRAL RESOLUTION - QUAD. GRID ')
  ENDIF
ELSEIF(NLIN == 1) THEN
  IF(NSMAX == 63) THEN
    NDGL = 64
  ELSEIF(NSMAX == 95) THEN
    NDGL = 96
  ELSEIF(NSMAX == 127) THEN
    NDGL = 128
  ELSEIF(NSMAX == 159) THEN
    NDGL = 160
  ELSEIF(NSMAX == 191) THEN
    NDGL = 192
  ELSEIF(NSMAX == 199) THEN
    NDGL = 200
  ELSEIF(NSMAX == 255) THEN
    NDGL = 256
  ELSEIF(NSMAX == 319) THEN
    NDGL = 320
  ELSEIF(NSMAX == 399) THEN
    NDGL = 400
  ELSEIF(NSMAX == 511) THEN
    NDGL = 512
  ELSEIF(NSMAX == 639) THEN
    NDGL = 640
  ELSEIF(NSMAX == 799) THEN
    NDGL = 800
  ELSEIF(NSMAX == 1023) THEN
    NDGL = 1024
  ELSEIF(NSMAX == 1279) THEN
    NDGL = 1280
  ELSEIF(NSMAX == 1599) THEN
    NDGL = 1600
  ELSEIF(NSMAX == 2047) THEN
    NDGL = 2048
  ELSEIF(NSMAX == 2559) THEN
    NDGL = 2560
  ELSEIF(NSMAX == 3999) THEN
    NDGL = 4000
  ELSEIF(NSMAX == 7999) THEN
    NDGL = 8000
  ELSE
    WRITE(NERR,*)' WRONG SPECTRAL RESOLUTION ',NSMAX,' LIN. GRID'
    CALL ABOR1('SPTOGP:UNSUPPORTED SPECTRAL RESOLUTION - LIN. GRID')
  ENDIF
ELSE
  WRITE(NERR,*)' WRONG NLIN=',NLIN
  CALL ABOR1('SPTOGP:WRONG NLIN')
ENDIF

END SUBROUTINE SETNDGL

!     ------------------------------------------------------------------

SUBROUTINE CHECK_NDGL

! Decide number of Gaussian latitudes given spectral truncation
! Only certain combinations of truncation/linear grid
! or quadratic grid are supported
 
IF(NLIN == 0) THEN
  IF(NDGL .ne.  32 .and. NDGL .ne.  64 .and. NDGL .ne.  96 .and. &
     NDGL .ne. 160 .and. NDGL .ne. 320 .and. NDGL .ne. 512 .and. &
     NDGL .ne. 640 .and. NDGL .ne. 800 .and. NDGL .ne. 1024 .and. NDGL .ne. 1280 ) THEN
    WRITE(NERR,*)' WRONG SPECTRAL RESOLUTION ',NSMAX,' QUAD. GRID'
!    CALL ABOR1('SPTOGP:UNSUPPORTED SPECTRAL RESOLUTION - QUAD. GRID ')
  ENDIF
ELSEIF(NLIN == 1) THEN
  IF(NDGL .ne. 32 .and. NDGL .ne.  64 .and. NDGL .ne.  96 .and. NDGL .ne. 128 .and. & 
     NDGL .ne. 160.and. NDGL .ne. 256 .and. NDGL .ne. 320 .and. NDGL .ne. 400 .and. &
     NDGL .ne. 512.and. NDGL .ne. 640 .and. NDGL .ne. 800 .and. NDGL .ne. 1024) THEN
    WRITE(NERR,*)' WRONG SPECTRAL RESOLUTION ',NSMAX,' LIN. GRID'
!    CALL ABOR1('SPTOGP:UNSUPPORTED SPECTRAL RESOLUTION - LIN. GRID')
  ENDIF
ELSE
  WRITE(NERR,*)' WRONG NLIN=',NLIN
!  CALL ABOR1('SPTOGP:WRONG NLIN')
ENDIF

END SUBROUTINE CHECK_NDGL

!     ------------------------------------------------------------------

SUBROUTINE MAXMINL

! Set MAX and MIN values for field depending on GRIB code

REAL(KIND=JPRB) :: ZMAX,ZMIN
INTEGER(KIND=JPIM) :: JFLD,JROF

DO JFLD=1,IFLD
  IF(IPARAM(JFLD) == 129) THEN
    ZMAX=60000._JPRB
    ZMIN=-6500._JPRB
  ELSEIF((IPARAM(JFLD) == 139.OR.IPARAM(JFLD) == 170 &
   &.OR.IPARAM(JFLD) == 183.OR.IPARAM(JFLD) == 235 &
   &.OR.IPARAM(JFLD) == 236)) THEN
    ZMAX=350._JPRB
    ZMIN=210._JPRB
  ELSEIF((IPARAM(JFLD) == 140.OR.IPARAM(JFLD) == 171 &
   &.OR.IPARAM(JFLD) == 184.OR.IPARAM(JFLD) == 237)) THEN
    ZMAX=0.033_JPRB
    ZMIN=2.E-6_JPRB
  ELSEIF(IPARAM(JFLD) == 246.OR.IPARAM(JFLD) == 247) THEN
    ZMAX=1.E-3_JPRB
    ZMIN=0.0_JPRB
  ELSEIF(IPARAM(JFLD) == 248) THEN
    ZMAX=1.0_JPRB
    ZMIN=0.0_JPRB
  ELSEIF(IPARAM(JFLD) == 141) THEN
    ZMAX=10._JPRB
    ZMIN=0.0_JPRB
  ELSEIF(IPARAM(JFLD) == 228141 .OR. IPARAM(JFLD) == 228038) THEN  
    ZMAX=10000._JPRB
    ZMIN=0.0_JPRB
  ELSEIF(IPARAM(JFLD) == 133) THEN
    ZMAX=1.E-1_JPRB
    ZMIN=1.E-8_JPRB
  ELSEIF(IPARAM(JFLD) == 160) THEN
    ZMAX=1900._JPRB
    ZMIN=0.0_JPRB
  ELSEIF((IPARAM(JFLD) == 161.OR.IPARAM(JFLD) == 172 &
   &.OR.IPARAM(JFLD) == 199)) THEN
    ZMAX=1.0_JPRB
    ZMIN=0.0_JPRB
  ELSEIF(IPARAM(JFLD) == 162) THEN
    ZMAX=1.57_JPRB
    ZMIN=-1.57_JPRB
  ELSEIF(IPARAM(JFLD) == 163) THEN
    ZMAX=0.11_JPRB
    ZMIN=0.0_JPRB
  ELSEIF(IPARAM(JFLD) == 173) THEN
    ZMAX=100._JPRB
    ZMIN=0.001_JPRB
  ELSEIF(IPARAM(JFLD) == 174) THEN
    ZMAX=0.6_JPRB
    ZMIN=0.07_JPRB
  ELSEIF(IPARAM(JFLD) == 198) THEN
    ZMAX=8.E-4_JPRB
    ZMIN=0.0_JPRB
  ELSEIF(IPARAM(JFLD) == 200) THEN
    ZMAX=4.E6_JPRB
    ZMIN=0.0_JPRB
  ELSEIF(IPARAM(JFLD) == 233) THEN
    ZMAX=0.033_JPRB
    ZMIN=8.8E-6_JPRB
  ELSEIF(IPARAM(JFLD) == 234) THEN
    ZMAX=-1.38_JPRB
    ZMIN=-20._JPRB
  ELSEIF((IPARAM(JFLD) == 238 .OR. IPARAM(JFLD) ==  35 .OR. &
   &      IPARAM(JFLD) ==  36 .OR. IPARAM(JFLD) ==  37 .OR. &
   &      IPARAM(JFLD) ==  38)) THEN
    ZMAX=273.16_JPRB
    ZMIN=210._JPRB
  ELSEIF((IPARAM(JFLD) ==  27 .OR. IPARAM(JFLD) ==  28 .OR. &
   &      IPARAM(JFLD) ==  31 .OR. IPARAM(JFLD) ==  32)) THEN
    ZMAX=1.
    ZMIN=0.
  ELSEIF((IPARAM(JFLD) ==  29 .OR. IPARAM(JFLD) ==  30)) THEN
    ZMAX=20.
    ZMIN=1.
  ELSEIF(IPARAM(JFLD) ==  33) THEN
    ZMAX=500.
    ZMIN=50.
  ELSEIF(IPARAM(JFLD) ==  34) THEN
    ZMAX=350.
    ZMIN=210.
  ELSEIF((IPARAM(JFLD) ==  39 .OR. IPARAM(JFLD) ==  40 .OR. &
   &      IPARAM(JFLD) ==  41 .OR. IPARAM(JFLD) ==  42)) THEN
    ZMAX=0.472_JPRB
    ZMIN=0._JPRB
  ELSEIF(IPARAM(JFLD) ==  138 .AND. (LSTDEV .OR. LVAR )) THEN
    ZMAX=1.E15_JPRB
    ZMIN=2.E-13_JPRB
  ELSEIF(IPARAM(JFLD) ==  157) THEN  ! Relative Humidity
    ZMAX=2.0
    ZMIN=1.E-7_JPRB
  ELSEIF(IPARAM(JFLD) ==  203) THEN  ! Ozone Mixing Ratio
    ZMAX=1.E-4_JPRB
    ZMIN=1.E-10_JPRB
  ELSE
    ZMAX=1.E30_JPRB
    ZMIN=-1.E30_JPRB
  ENDIF

! IF(LVAR .AND. IPARAM(JFLD) == 138) THEN
!   ZMAX=1.E30_JPRB
!   ZMIN=2.E-13_JPRB
!   print*,'SPTOGP: APPLIED LVAR MIN VALUES LIMIT'
! ELSEIF(LVAR .AND. IPARAM(JFLD) == 133) THEN
!   ZMAX=1.E-1_JPRB
!   ZMIN=1.E-20_JPRB
!   print*,'SPTOGP: APPLIED LVAR MIN VALUES LIMIT'
! ENDIF

  IF(LVAR) THEN
    ZMAX=ZMAX*ZMAX
    ZMIN=ZMIN*ZMIN
    print*,'SPTOGP: APPLIED LVAR MAX-MIN VALUES LIMIT'
  ENDIF

  DO JROF=1,NGPTOT
    ZG(JROF,JFLD,1) = MAX(ZMIN,MIN(ZMAX,ZG(JROF,JFLD,1)))
  ENDDO
ENDDO

END SUBROUTINE MAXMINL

!     ------------------------------------------------------------------

END PROGRAM SPTOGP


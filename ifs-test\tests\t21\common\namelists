# Namelists that can be shared by all tests.
# Note that some tests may not use all namelists present here;
# e.g. tests adj, sv and tl_taylor do not read namelist NAMAFN.

# Settings that may be overwritten in individual tests:
# NAMDIM
NPROMA=${NPROMA:-"-24"}

# NAMPAR0
LDETAILED_STATS=${LDETAILED_STATS:-".TRUE."}
MBX_SIZE=${MBX_SIZE:-"1000000"}

# NAMPAR1
# Additional options as needed:
NAMPAR1_EXTRA=${NAMPAR1_EXTRA:-""}

# NAMCT0
LFDBOP=${LFDBOP:-".FALSE."}
NFRHIS=${NFRHIS:-"99"}
NFRPOS=${NFRPOS:-"99"}
NFPOS=${NFPOS:-"2"}
NFRDHP=${NFRDHP:-"1"}
NFRDHFD=${NFRDHFD:-"1"}
NUNDEFLD=${NUNDEFLD:-"-99999999"}
# Additional options as needed:
NAMCT0_EXTRA=${NAMCT0_EXTRA:-""}

# NAMARG
NCONF=${NCONF:-"1"}
CNMEXP=${CNMEXP:-"'epc8'"}

# NAMRIP
CSTOP=${CSTOP:-"'t6'"}
TSTEP=${TSTEP:-"3600.000000"}

# NAEPHY
LEPHYS=${LEPHYS:-".TRUE."}
LSLPHY=${LSLPHY:-".TRUE."}
LERADI=${LERADI:-".TRUE."}
LELAIV=${LELAIV:-".FALSE."}
LEO3CH=${LEO3CH:-".FALSE."}
# Additional options as needed:
NAEPHY_EXTRA=${NAEPHY_EXTRA:-""}

# NAMDPHY
NVEXTR=${NVEXTR:-"0"}
NCEXTR=${NCEXTR:-"0"}
NAMGEM_EXTRA=${NAMGEM_EXTRA:-""}

# Create namelist file:
cat > fort.4 << EOF
&NAMDIM
NPROMA=$NPROMA,
/
&NAMPAR0
LSTATS=.TRUE.,
LDETAILED_STATS=$LDETAILED_STATS,
LSYNCSTATS=.FALSE.,
MP_TYPE=2,
MBX_SIZE=$MBX_SIZE,
NPROC=$NPROC,
NOUTPUT=1,
/
&NAMPAR1
LSPLIT=.TRUE.,
NFLDIN=0,
NSTRIN=1,
NSTROUT=0,
NOUTTYPE=1,
$NAMPAR1_EXTRA
/
&NAMCT0
NFRSDI=1,
N3DINI=0,
LREFOUT=.FALSE.,
LFDBOP=$LFDBOP,
NFRHIS=$NFRHIS,
NFRPOS=$NFRPOS,
NFPOS=$NFPOS,
NFRDHP=$NFRDHP,
NFRDHFD=$NFRDHFD,
NUNDEFLD=$NUNDEFLD,
$NAMCT0_EXTRA
/
&NAMARG
LECMWF=.TRUE.,
NCONF=$NCONF,
CNMEXP=$CNMEXP,
/
&NAMRIP
CSTOP=$CSTOP,
TSTEP=$TSTEP,
/
&NAEPHY
NALBEDOSCHEME=1,
LEPHYS=$LEPHYS,
LSLPHY=$LSLPHY,
LERADI=$LERADI,
LELAIV=$LELAIV,
LEO3CH=$LEO3CH,
LESNML=.true.,
NSNMLWS=2,
$NAEPHY_EXTRA
/
&NAMGEM
NHTYP=0,
$NAMGEM_EXTRA
/
&NAMAFN
TFP_FUA(1)%LLGP=.FALSE.,
/
&NAMDPHY
NVEXTR=$NVEXTR,
NCEXTR=$NCEXTR,
NCSNEC=5,
/
&NAMGRIB
NCYCLE=147,
$NAMGRIB_EXTRA
/
EOF

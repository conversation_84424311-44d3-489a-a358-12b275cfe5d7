# Source common environment variables
. ../common/env_vars

# Pre-empt some common namelist settings.
# NAMCT0:
NFRHIS="6"
NFRPOS="6"

# Include common namelists
. ../common/namelists

cat >> fort.4 << EOF
&NAMSPP
  LSPP=true,
  CSPP_MODEL_NAME='ifs',
/
&NAM_SPP_ACTIVE
  !
  !   request any of the defined perturbations
  !
  requested_perts='CFM','CFM1','CFM2','CFM3','RKAP','RKAP1','RKAP2','RKAP3','TOFDC','HSDT','VDEXC','ENTRORG','ENTSHALP','DETRPEN','RPRCON','RTAU','CUDUDV','CUDUDVS','RAMID','RCLDIFF','RCLCRIT','RLCRITSNOW','RAINEVAP','SNOWSUBLIM','QSATVERVEL','ZDECORR','ZSIG<PERSON>C<PERSON>','<PERSON>RADE<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','ZHS_VDAERO','DELTA_AERO','<PERSON>L<PERSON><PERSON>DINHOM','ENTSTPC1',
/
&NAM_SPP_MODIF
/
&NAMGFL
YQ_NL%LGP=true,
YQ_NL%LSP=false,
YO3_NL%LGP=false,
YQ_NL%LGPINGP=true,
YR_NL%NREQIN=-1,
YS_NL%NREQIN=-1,
/
 &NAERAD
 NRPROMA=-8,
 CRTABLEDIR="/home/<USER>/rdx/data/ifs/",
 LAER3D=.false.,
 /
 &NAMFPC
    CFPFMT='MODEL',
    NFP3DFS=2,
    MFP3DFS=130,133,
    NRFP3S=-99,
    NFP2DF=1,
    MFP2DF=152,
    NFP3DFP=1,
    MFP3DFP=130,
    RFP3P=100000.,85000.,70000.,50000.,
    NFPPHY=0,
    MFPPHY=129,136,137,139,
 /
EOF

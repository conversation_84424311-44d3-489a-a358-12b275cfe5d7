DIFF=$(grep 'SUMMED TIME IN COMMUNICATIONS   = [ 0-9.]* SECONDS [ 0-9.]* PERCENT OF TOTAL' NODE.001_01 | tr -s " " | cut -d" " -f8)
for IDIFF in ${DIFF} ; do
    if (($(bc <<< "$IDIFF>0.0"))); then echo "Passed: $IDIFF > 0"; else echo "Test failed: Time in communications must be greater than 0"; exit 1; fi
done
DIFF=$(grep 'CURRENT NUMBER OF THREADS[ 0-9]*$' NODE.001_01 | tr -s " " | cut -d" " -f6)
if [[ -n "$IFS_TEST_PARALLEL_OVERRIDE-}" ]] || (($(bc <<< "$NTHREAD==2"))); then : ; else echo "Test failed: The number of threads was incorrect"; exit 1; fi
if (($(bc <<< "$DIFF==$NTHREAD"))); then echo "Passed"; else echo "Test failed: The number of threads was incorrect"; exit 1; fi

./ifs-check-bitid

# Source common environment variables
. ../common/env_vars

# Pre-empt some common namelist settings.
# NAMARG:
CNMEXP="'hmec'"

# NAEPHY:
LEO3CH=".TRUE."
NAEPHY_EXTRA="LO3CH_HLO=.FALSE.,"

# NAMDPHY:
NVEXTR="7"
NCEXTR="74"

# NAMCT0:
NFRPOS=3
# NAMCT0_EXTRA="NSPPR=1,"  # uncomment for per-level norms

# NAMRIP:
TSTEP="1800.000000"

# NAMGRIB:
NAMGRIB_EXTRA="
CFCLASS='rd',
CTYPE='fc',"

# Include common namelists
. ../common/namelists

mv fort.4 common_namelists

#Start set up namelist_model1 by extracting from common one
namelists=(NAMRIP NAEPHY NAMDPHY NAMGRIB)
rm -f namelist_model1
for namelist in ${namelists[@]}; do
  awk "/^\s*&$namelist\s*$/{flag=1}/^\s*\//{if (flag) {print;exit}}flag" common_namelists >> namelist_model1
done

# Add specific namelist_model1 settings
cat >> namelist_model1 << EOF
&NAMDYNA
LGRADSP=.FALSE.,
/
&NAMGFL
NAERO=0,
NACTAERO=0,
NCHEM=0,
YQ_NL%LGP=.TRUE.,
YQ_NL%LSP=.FALSE.,
YO3_NL%LGP=.TRUE.,
YQ_NL%LGPINGP=.TRUE.,
YL_NL%LGP=.TRUE.,
YI_NL%LGP=.TRUE.,
YA_NL%LGP=.TRUE.,
/
&NAERAD
CRTABLEDIR='./ifsdata/'
LECOMPGRID=.FALSE.,
RMUZUV=0.01,
NUVTIM=72,
NRADUV=1,
NUV=24,
LUVPROC=.TRUE.,
LUVAERP=.TRUE.,
KMODTS=2,
NRPROMA=0,
LEPO3RA=.TRUE.,
NGHGRAD=20,
NAERMACC=0,
LAER3D=.FALSE.,
/
&NAMNMI
LNMIRQ=.FALSE.,
LASSI=.FALSE.,
/
&NAMFPC
CFPFMT="MODEL",
NFP3DFS=4,
NFP3DFP=2,
MFP3DFS(:)=210001,210048,215184,210203
MFP3DFP(:)=210011,217003
NFP2DF=1,
MFP2DF(:)=152,
NFPPHY=21,
MFPPHY(:)=210031,210119,215022,215020,210208,210052,210073,214002,210207,215104,210217,215099,215120,215143,215179,215028,215032,215055,215058,215173,210206,
NRFP3S(:)=-99,
RFP3P=100000.,85000.,70000.,50000.,
/
&NAMCOMPO
/
&NAMCHEM
/
EOF

# Add empty defaults for this cycle
cat ${NAMELIST_INIT} >> namelist_model1


# Regenerate common namelists with model2 settings:
# NAMDPHY:
NCEXTR="79"

# NAMRIP:
TSTEP="3600.000000"

# NAMGRIB:
NAMGRIB_EXTRA=

# Recreate fort.4
. ../common/namelists
mv fort.4 common_namelists

#Start set up namelist_model2 by extracting from common one
rm -f namelist_model2
for namelist in ${namelists[@]}; do
  awk "/^\s*&$namelist\s*$/{flag=1}/^\s*\//{if (flag) {print;exit}}flag" common_namelists >> namelist_model2
done
# Add specific namelist_model2 settings
cat >>  namelist_model2 << EOF
&NAMDYNA
LGRADSP=.FALSE.,
/
&NAMGFL
NGHG=2,
YLRCH4_NL%CNAME='kCH4',
YLRCH4_NL%IGRBCODE=210071,
YLRCH4_NL%LGP=.TRUE.,YLRCH4_NL%LTRAJIO=.FALSE.,
YGHG_NL(1)%CNAME='CO2_GHG',
YGHG_NL(1)%IGRBCODE=210061,
YGHG_NL(1)%LADV5=.TRUE.,
YGHG_NL(1)%LASSIM=.TRUE.,
YGHG_NL(1)%LMASSFIX=.TRUE.,
YGHG_NL(1)%LNEGFIX=.FALSE.,
YGHG_NL(1)%LQM=.FALSE.,
YGHG_NL(1)%LQM3D=.TRUE.,
YGHG_NL(1)%BETAMFBC=2,
YGHG_NL(2)%CNAME='CH4_GHG',
YGHG_NL(2)%IGRBCODE=210062,
YGHG_NL(2)%LADV5=.TRUE.,
YGHG_NL(2)%LASSIM=.TRUE.,
YGHG_NL(2)%LMASSFIX=.TRUE.,
YGHG_NL(2)%LNEGFIX=.FALSE.,
YGHG_NL(2)%LQM=.FALSE.,
YGHG_NL(2)%LQM3D=.TRUE.,
YGHG_NL(2)%BETAMFBC=2,
NAERO=15,
NACTAERO=14,
YAERO_NL(1)%CNAME='Sea-salt_1',
YAERO_NL(1)%IGRBCODE=210001,
YAERO_NL(1)%LMASSFIX=.TRUE.,
YAERO_NL(1)%LQM=.TRUE.,
YAERO_NL(1)%LQM3D=.FALSE.,
YAERO_NL(1)%BETAMFBC=-999,
YAERO_NL(2)%CNAME='Sea-salt_2',
YAERO_NL(2)%IGRBCODE=210002,
YAERO_NL(2)%LMASSFIX=.TRUE.,
YAERO_NL(2)%LQM=.TRUE.,
YAERO_NL(2)%LQM3D=.FALSE.,
YAERO_NL(2)%BETAMFBC=-999,
YAERO_NL(3)%CNAME='Sea-salt_3',
YAERO_NL(3)%IGRBCODE=210003,
YAERO_NL(3)%LMASSFIX=.TRUE.,
YAERO_NL(3)%LQM=.TRUE.,
YAERO_NL(3)%LQM3D=.FALSE.,
YAERO_NL(3)%BETAMFBC=-999,
YAERO_NL(4)%CNAME='Desert-dust_1',
YAERO_NL(4)%IGRBCODE=210004,
YAERO_NL(4)%LMASSFIX=.TRUE.,
YAERO_NL(4)%LQM=.TRUE.,
YAERO_NL(4)%LQM3D=.FALSE.,
YAERO_NL(4)%BETAMFBC=-999,
YAERO_NL(5)%CNAME='Desert-dust_2',
YAERO_NL(5)%IGRBCODE=210005,
YAERO_NL(5)%LMASSFIX=.TRUE.,
YAERO_NL(5)%LQM=.TRUE.,
YAERO_NL(5)%LQM3D=.FALSE.,
YAERO_NL(5)%BETAMFBC=-999,
YAERO_NL(6)%CNAME='Desert-dust_3',
YAERO_NL(6)%IGRBCODE=210006,
YAERO_NL(6)%LMASSFIX=.TRUE.,
YAERO_NL(6)%LQM=.TRUE.,
YAERO_NL(6)%LQM3D=.FALSE.,
YAERO_NL(6)%BETAMFBC=-999,
YAERO_NL(7)%CNAME='Organic-matter_A',
YAERO_NL(7)%IGRBCODE=210007,
YAERO_NL(7)%LMASSFIX=.TRUE.,
YAERO_NL(7)%LQM=.TRUE.,
YAERO_NL(7)%LQM3D=.FALSE.,
YAERO_NL(7)%BETAMFBC=-999,
YAERO_NL(8)%CNAME='Organic-matter_B',
YAERO_NL(8)%IGRBCODE=210008,
YAERO_NL(8)%LMASSFIX=.TRUE.,
YAERO_NL(8)%LQM=.TRUE.,
YAERO_NL(8)%LQM3D=.FALSE.,
YAERO_NL(8)%BETAMFBC=-999,
YAERO_NL(9)%CNAME='Black-carbon_A',
YAERO_NL(9)%IGRBCODE=210009,
YAERO_NL(9)%LMASSFIX=.TRUE.,
YAERO_NL(9)%LQM=.TRUE.,
YAERO_NL(9)%LQM3D=.FALSE.,
YAERO_NL(9)%BETAMFBC=-999,
YAERO_NL(10)%CNAME='Black-carbon_B',
YAERO_NL(10)%IGRBCODE=210010,
YAERO_NL(10)%LMASSFIX=.TRUE.,
YAERO_NL(10)%LQM=.TRUE.,
YAERO_NL(10)%LQM3D=.FALSE.,
YAERO_NL(10)%BETAMFBC=-999,
YAERO_NL(11)%CNAME='Sulphate_SO4',
YAERO_NL(11)%IGRBCODE=210011,
YAERO_NL(11)%LMASSFIX=.TRUE.,
YAERO_NL(11)%LQM=.TRUE.,
YAERO_NL(11)%LQM3D=.FALSE.,
YAERO_NL(11)%BETAMFBC=-999,
YAERO_NL(12)%CNAME='Nitrate_1',
YAERO_NL(12)%IGRBCODE=210247,
YAERO_NL(12)%LMASSFIX=.TRUE.,
YAERO_NL(12)%LQM=.TRUE.,
YAERO_NL(12)%LQM3D=.FALSE.,
YAERO_NL(12)%BETAMFBC=-999,
YAERO_NL(13)%CNAME='Nitrate_2',
YAERO_NL(13)%IGRBCODE=210248,
YAERO_NL(13)%LMASSFIX=.TRUE.,
YAERO_NL(13)%LQM=.TRUE.,
YAERO_NL(13)%LQM3D=.FALSE.,
YAERO_NL(13)%BETAMFBC=-999,
YAERO_NL(14)%CNAME='Ammonium',
YAERO_NL(14)%IGRBCODE=210249,
YAERO_NL(14)%LMASSFIX=.TRUE.,
YAERO_NL(14)%LQM=.TRUE.,
YAERO_NL(14)%LQM3D=.FALSE.,
YAERO_NL(14)%BETAMFBC=-999,
YAERO_NL(15)%CNAME='Total_aerosol',
YAERO_NL(15)%IGRBCODE=210048,
YAERO_NL(15)%LMASSFIX=.TRUE.,
YAERO_NL(15)%LQM=.TRUE.,
YAERO_NL(15)%LQM3D=.FALSE.,
YAERO_NL(15)%BETAMFBC=-999,
YAERO_NL(15)%LADV5=.TRUE.,
LAERAOT=.TRUE.,
LAERLISI=.FALSE.,

NAEROUT=1,
YAEROUT_NL(1)%CNAME='AEROUT-7',
YAEROUT_NL(1)%IGRBCODE=210022,
NUVP=1,
YUVP_NL(1)%CNAME='UVP2DRAD',
YUVP_NL(1)%IGRBCODE=210055,
NAERO_WVL_DIAG=20,
NAERO_WVL_DIAG_TYPES=5,
YAERO_WVL_DIAG_NL(1)%IWVL=550,
YAERO_WVL_DIAG_NL(1)%IGRIBDIAG=210207,215104,215122,215140,215158,
YAERO_WVL_DIAG_NL(2)%IWVL=340,
YAERO_WVL_DIAG_NL(2)%IGRIBDIAG=210217,215096,215114,215132,215150,
YAERO_WVL_DIAG_NL(3)%IWVL=355,
YAERO_WVL_DIAG_NL(3)%IGRIBDIAG=210218,215097,215115,215133,215151,
YAERO_WVL_DIAG_NL(4)%IWVL=380,
YAERO_WVL_DIAG_NL(4)%IGRIBDIAG=210219,215098,215116,215134,215152,
YAERO_WVL_DIAG_NL(5)%IWVL=400,
YAERO_WVL_DIAG_NL(5)%IGRIBDIAG=210220,215099,215117,215135,215153,
YAERO_WVL_DIAG_NL(6)%IWVL=440,
YAERO_WVL_DIAG_NL(6)%IGRIBDIAG=210221,215100,215118,215136,215154,
YAERO_WVL_DIAG_NL(7)%IWVL=469,
YAERO_WVL_DIAG_NL(7)%IGRIBDIAG=210213,215101,215119,215137,215155,
YAERO_WVL_DIAG_NL(8)%IWVL=500,
YAERO_WVL_DIAG_NL(8)%IGRIBDIAG=210222,215102,215120,215138,215156,
YAERO_WVL_DIAG_NL(9)%IWVL=532,
YAERO_WVL_DIAG_NL(9)%IGRIBDIAG=210223,215103,215121,215139,215157,
YAERO_WVL_DIAG_NL(10)%IWVL=645,
YAERO_WVL_DIAG_NL(10)%IGRIBDIAG=210224,215105,215123,215141,215159,
YAERO_WVL_DIAG_NL(11)%IWVL=670,
YAERO_WVL_DIAG_NL(11)%IGRIBDIAG=210214,215106,215124,215142,215160,
YAERO_WVL_DIAG_NL(12)%IWVL=800,
YAERO_WVL_DIAG_NL(12)%IGRIBDIAG=210225,215107,215125,215143,215161,
YAERO_WVL_DIAG_NL(13)%IWVL=858,
YAERO_WVL_DIAG_NL(13)%IGRIBDIAG=210226,215108,215126,215144,215162,
YAERO_WVL_DIAG_NL(14)%IWVL=865,
YAERO_WVL_DIAG_NL(14)%IGRIBDIAG=210215,215109,215127,215145,215163,
YAERO_WVL_DIAG_NL(15)%IWVL=1020,
YAERO_WVL_DIAG_NL(15)%IGRIBDIAG=210227,215110,215128,215146,215164,
YAERO_WVL_DIAG_NL(16)%IWVL=1064,
YAERO_WVL_DIAG_NL(16)%IGRIBDIAG=210228,215111,215129,215147,215165,
YAERO_WVL_DIAG_NL(17)%IWVL=1240,
YAERO_WVL_DIAG_NL(17)%IGRIBDIAG=210216,215112,215130,215148,215166,
YAERO_WVL_DIAG_NL(18)%IWVL=1640,
YAERO_WVL_DIAG_NL(18)%IGRIBDIAG=210229,215113,215131,215149,215167,
YAERO_WVL_DIAG_NL(19)%IWVL=2130,
YAERO_WVL_DIAG_NL(19)%IGRIBDIAG=210230,215176,215177,215178,215179,
YAERO_WVL_DIAG_NL(20)%IWVL=10000,
YAERO_WVL_DIAG_NL(20)%IGRIBDIAG=216008,216009,216010,216011,216012,
NCHEM=57,
YCHEM_NL(1)%CNAME='O3',
YCHEM_NL(1)%NREQIN=1,
YCHEM_NL(1)%RMOLMASS=48.0,
YCHEM_NL(1)%IGRBCODE=210203,
YCHEM_NL(1)%LNEGFIX=.TRUE.,
YCHEM_NL(1)%IGRIBTC=210206,
YCHEM_NL(1)%IGRIBDV=221001,
YCHEM_NL(1)%HENRYA=1.0e-2,
YCHEM_NL(1)%HENRYB=2800.0,
YCHEM_NL(1)%LMASSFIX=.TRUE.,
YCHEM_NL(1)%BETAMFBC=-999.9,
YCHEM_NL(1)%LASSIM=.TRUE.,
YCHEM_NL(1)%LADV5=.TRUE.,
YCHEM_NL(2)%CNAME='NOx',
YCHEM_NL(2)%NREQIN=1,
YCHEM_NL(2)%RMOLMASS=14.0,
YCHEM_NL(2)%IGRBCODE=210129,
YCHEM_NL(2)%LNEGFIX=.TRUE.,
YCHEM_NL(2)%IGRIBTC=210130,
YCHEM_NL(2)%LMASSFIX=.TRUE.,
YCHEM_NL(2)%BETAMFBC=-999.9,
YCHEM_NL(2)%LASSIM=.FALSE.,
YCHEM_NL(3)%CNAME='H2O2',
YCHEM_NL(3)%NREQIN=1,
YCHEM_NL(3)%RMOLMASS=34.0,
YCHEM_NL(3)%IGRBCODE=217003,
YCHEM_NL(3)%LNEGFIX=.TRUE.,
YCHEM_NL(3)%IGRIBTC=218003,
YCHEM_NL(3)%IGRIBDV=221003,
YCHEM_NL(3)%HENRYA=8.3e4,
YCHEM_NL(3)%HENRYB=7600.0,
YCHEM_NL(3)%LMASSFIX=.TRUE.,
YCHEM_NL(3)%BETAMFBC=-999.9,
YCHEM_NL(3)%LASSIM=.FALSE.,
YCHEM_NL(4)%CNAME='CH4',
YCHEM_NL(4)%NREQIN=1,
YCHEM_NL(4)%RMOLMASS=16.0,
YCHEM_NL(4)%IGRBCODE=217004,
YCHEM_NL(4)%LNEGFIX=.TRUE.,
YCHEM_NL(4)%IGRIBTC=218004,
YCHEM_NL(4)%LMASSFIX=.TRUE.,
YCHEM_NL(4)%BETAMFBC=-999.9,
YCHEM_NL(4)%LASSIM=.FALSE.,
YCHEM_NL(5)%CNAME='CO',
YCHEM_NL(5)%NREQIN=1,
YCHEM_NL(5)%RMOLMASS=28.0,
YCHEM_NL(5)%IGRBCODE=210123,
YCHEM_NL(5)%LNEGFIX=.TRUE.,
YCHEM_NL(5)%IGRIBTC=210127,
YCHEM_NL(5)%IGRIBDV=221005,
YCHEM_NL(5)%HENRYA=9.7e-4,
YCHEM_NL(5)%HENRYB=1300.0,
YCHEM_NL(5)%LMASSFIX=.TRUE.,
YCHEM_NL(5)%BETAMFBC=-999.9,
YCHEM_NL(5)%LASSIM=.TRUE.,
YCHEM_NL(5)%LADV5=.TRUE.,
YCHEM_NL(6)%CNAME='HNO3',
YCHEM_NL(6)%NREQIN=1,
YCHEM_NL(6)%RMOLMASS=63.0,
YCHEM_NL(6)%IGRBCODE=217006,
YCHEM_NL(6)%LNEGFIX=.TRUE.,
YCHEM_NL(6)%IGRIBTC=218006,
YCHEM_NL(6)%IGRIBDV=221006,
YCHEM_NL(6)%HENRYA=3.2e11,
YCHEM_NL(6)%HENRYB=8700.0,
YCHEM_NL(6)%LMASSFIX=.TRUE.,
YCHEM_NL(6)%BETAMFBC=-999.9,
YCHEM_NL(6)%LASSIM=.FALSE.,
YCHEM_NL(7)%CNAME='CH3OOH',
YCHEM_NL(7)%NREQIN=1,
YCHEM_NL(7)%RMOLMASS=48.0,
YCHEM_NL(7)%IGRBCODE=217007,
YCHEM_NL(7)%LNEGFIX=.TRUE.,
YCHEM_NL(7)%IGRIBTC=218007,
YCHEM_NL(7)%IGRIBDV=221007,
YCHEM_NL(7)%HENRYA=2.9e2,
YCHEM_NL(7)%HENRYB=5200.0,
YCHEM_NL(7)%LMASSFIX=.TRUE.,
YCHEM_NL(7)%BETAMFBC=-999.9,
YCHEM_NL(7)%LASSIM=.FALSE.,
YCHEM_NL(8)%CNAME='CH2O',
YCHEM_NL(8)%NREQIN=1,
YCHEM_NL(8)%RMOLMASS=30.0,
YCHEM_NL(8)%IGRBCODE=210124,
YCHEM_NL(8)%LNEGFIX=.TRUE.,
YCHEM_NL(8)%IGRIBTC=210128,
YCHEM_NL(8)%IGRIBDV=221008,
YCHEM_NL(8)%HENRYA=3.2e3,
YCHEM_NL(8)%HENRYB=6800.0,
YCHEM_NL(8)%LMASSFIX=.TRUE.,
YCHEM_NL(8)%BETAMFBC=-999.9,
YCHEM_NL(8)%LASSIM=.TRUE.,
YCHEM_NL(8)%LADV5=.TRUE.,
YCHEM_NL(9)%CNAME='PAR',
YCHEM_NL(9)%NREQIN=1,
YCHEM_NL(9)%RMOLMASS=12.0,
YCHEM_NL(9)%IGRBCODE=217009,
YCHEM_NL(9)%LNEGFIX=.TRUE.,
YCHEM_NL(9)%IGRIBTC=218009,
YCHEM_NL(9)%LMASSFIX=.TRUE.,
YCHEM_NL(9)%BETAMFBC=-999.9,
YCHEM_NL(9)%LASSIM=.FALSE.,
YCHEM_NL(10)%CNAME='C2H4',
YCHEM_NL(10)%NREQIN=1,
YCHEM_NL(10)%RMOLMASS=28.0,
YCHEM_NL(10)%IGRBCODE=217010,
YCHEM_NL(10)%LNEGFIX=.TRUE.,
YCHEM_NL(10)%IGRIBTC=218010,
YCHEM_NL(10)%LMASSFIX=.TRUE.,
YCHEM_NL(10)%BETAMFBC=-999.9,
YCHEM_NL(10)%LASSIM=.FALSE.,
YCHEM_NL(11)%CNAME='OLE',
YCHEM_NL(11)%NREQIN=1,
YCHEM_NL(11)%RMOLMASS=24.0,
YCHEM_NL(11)%IGRBCODE=217011,
YCHEM_NL(11)%LNEGFIX=.TRUE.,
YCHEM_NL(11)%IGRIBTC=218011,
YCHEM_NL(11)%LMASSFIX=.TRUE.,
YCHEM_NL(11)%BETAMFBC=-999.9,
YCHEM_NL(11)%LASSIM=.FALSE.,
YCHEM_NL(12)%CNAME='ALD2',
YCHEM_NL(12)%NREQIN=1,
YCHEM_NL(12)%RMOLMASS=24.0,
YCHEM_NL(12)%IGRBCODE=217012,
YCHEM_NL(12)%LNEGFIX=.TRUE.,
YCHEM_NL(12)%IGRIBTC=218012,
YCHEM_NL(12)%IGRIBDV=221012,
YCHEM_NL(12)%HENRYA=17.0,
YCHEM_NL(12)%HENRYB=5000.0,
YCHEM_NL(12)%LMASSFIX=.TRUE.,
YCHEM_NL(12)%BETAMFBC=-999.9,
YCHEM_NL(12)%LASSIM=.FALSE.,
YCHEM_NL(13)%CNAME='PAN',
YCHEM_NL(13)%NREQIN=1,
YCHEM_NL(13)%RMOLMASS=121.0,
YCHEM_NL(13)%IGRBCODE=217013,
YCHEM_NL(13)%LNEGFIX=.TRUE.,
YCHEM_NL(13)%IGRIBTC=218013,
YCHEM_NL(13)%IGRIBDV=221013,
YCHEM_NL(13)%HENRYA=2.9e+0,
YCHEM_NL(13)%HENRYB=5700.0,
YCHEM_NL(13)%LMASSFIX=.TRUE.,
YCHEM_NL(13)%BETAMFBC=-999.9,
YCHEM_NL(13)%LASSIM=.FALSE.,
YCHEM_NL(14)%CNAME='ROOH',
YCHEM_NL(14)%NREQIN=1,
YCHEM_NL(14)%RMOLMASS=47.0,
YCHEM_NL(14)%IGRBCODE=217014,
YCHEM_NL(14)%LNEGFIX=.TRUE.,
YCHEM_NL(14)%IGRIBTC=218014,
YCHEM_NL(14)%IGRIBDV=221014,
YCHEM_NL(14)%HENRYA=340.0,
YCHEM_NL(14)%HENRYB=6000.0,
YCHEM_NL(14)%LMASSFIX=.TRUE.,
YCHEM_NL(14)%BETAMFBC=-999.9,
YCHEM_NL(14)%LASSIM=.FALSE.,
YCHEM_NL(15)%CNAME='ONIT',
YCHEM_NL(15)%NREQIN=1,
YCHEM_NL(15)%RMOLMASS=77.0,
YCHEM_NL(15)%IGRBCODE=217015,
YCHEM_NL(15)%LNEGFIX=.TRUE.,
YCHEM_NL(15)%IGRIBTC=218015,
YCHEM_NL(15)%IGRIBDV=221015,
YCHEM_NL(15)%HENRYA=1.0e3,
YCHEM_NL(15)%HENRYB=6485,
YCHEM_NL(15)%LMASSFIX=.TRUE.,
YCHEM_NL(15)%BETAMFBC=-999.9,
YCHEM_NL(15)%LASSIM=.FALSE.,
YCHEM_NL(16)%CNAME='C5H8',
YCHEM_NL(16)%NREQIN=1,
YCHEM_NL(16)%RMOLMASS=68.1,
YCHEM_NL(16)%IGRBCODE=217016,
YCHEM_NL(16)%LNEGFIX=.TRUE.,
YCHEM_NL(16)%IGRIBTC=218016,
YCHEM_NL(16)%IGRIBDV=221016,
YCHEM_NL(16)%HENRYA=1.3e-2,
YCHEM_NL(16)%HENRYB=0.0,
YCHEM_NL(16)%LMASSFIX=.TRUE.,
YCHEM_NL(16)%BETAMFBC=-999.9,
YCHEM_NL(16)%LASSIM=.FALSE.,
YCHEM_NL(17)%CNAME='SO2',
YCHEM_NL(17)%NREQIN=1,
YCHEM_NL(17)%RMOLMASS=64.1,
YCHEM_NL(17)%IGRBCODE=210122,
YCHEM_NL(17)%LNEGFIX=.TRUE.,
YCHEM_NL(17)%IGRIBTC=210126,
YCHEM_NL(17)%IGRIBDV=221017,
YCHEM_NL(17)%HENRYA=1.0e5,
YCHEM_NL(17)%HENRYB=3000.0,
YCHEM_NL(17)%LMASSFIX=.TRUE.,
YCHEM_NL(17)%BETAMFBC=-999.9,
YCHEM_NL(17)%LASSIM=.TRUE.,
YCHEM_NL(17)%LADV5=.TRUE.,
YCHEM_NL(18)%CNAME='DMS',
YCHEM_NL(18)%NREQIN=1,
YCHEM_NL(18)%RMOLMASS=62.1,
YCHEM_NL(18)%IGRBCODE=217018,
YCHEM_NL(18)%LNEGFIX=.TRUE.,
YCHEM_NL(18)%IGRIBTC=218018,
YCHEM_NL(18)%LMASSFIX=.TRUE.,
YCHEM_NL(18)%BETAMFBC=-999.9,
YCHEM_NL(18)%LASSIM=.FALSE.,
YCHEM_NL(19)%CNAME='NH3',
YCHEM_NL(19)%NREQIN=1,
YCHEM_NL(19)%RMOLMASS=17.0,
YCHEM_NL(19)%IGRBCODE=217019,
YCHEM_NL(19)%LNEGFIX=.TRUE.,
YCHEM_NL(19)%IGRIBTC=218019,
YCHEM_NL(19)%IGRIBDV=221019,
YCHEM_NL(19)%HENRYA=59.0,
YCHEM_NL(19)%HENRYB=4200.0,
YCHEM_NL(19)%LMASSFIX=.TRUE.,
YCHEM_NL(19)%BETAMFBC=-999.9,
YCHEM_NL(19)%LASSIM=.FALSE.,
YCHEM_NL(20)%CNAME='SO4',
YCHEM_NL(20)%NREQIN=-1,
YCHEM_NL(20)%RMOLMASS=96.1,
YCHEM_NL(20)%IGRBCODE=217020,
YCHEM_NL(20)%LNEGFIX=.FALSE.,
YCHEM_NL(20)%IGRIBTC=218020,
YCHEM_NL(20)%LMASSFIX=.TRUE.,
YCHEM_NL(20)%BETAMFBC=-999.9,
YCHEM_NL(20)%LASSIM=.FALSE.,
YCHEM_NL(21)%CNAME='NH4',
YCHEM_NL(21)%NREQIN=1,
YCHEM_NL(21)%RMOLMASS=18.0,
YCHEM_NL(21)%IGRBCODE=217021,
YCHEM_NL(21)%LNEGFIX=.TRUE.,
YCHEM_NL(21)%IGRIBTC=218021,
YCHEM_NL(21)%IGRIBDV=221021,
YCHEM_NL(21)%HENRYA=3.2e11,
YCHEM_NL(21)%HENRYB=8700.0,
YCHEM_NL(21)%LMASSFIX=.TRUE.,
YCHEM_NL(21)%BETAMFBC=-999.9,
YCHEM_NL(21)%LASSIM=.FALSE.,
YCHEM_NL(22)%CNAME='MSA',
YCHEM_NL(22)%NREQIN=1,
YCHEM_NL(22)%RMOLMASS=96.1,
YCHEM_NL(22)%IGRBCODE=217022,
YCHEM_NL(22)%LNEGFIX=.TRUE.,
YCHEM_NL(22)%IGRIBTC=218022,
YCHEM_NL(22)%HENRYA=3.2e11,
YCHEM_NL(22)%HENRYB=8700.0,
YCHEM_NL(22)%LMASSFIX=.TRUE.,
YCHEM_NL(22)%BETAMFBC=-999.9,
YCHEM_NL(22)%LASSIM=.FALSE.,
YCHEM_NL(23)%CNAME='CH3COCHO',
YCHEM_NL(23)%NREQIN=1,
YCHEM_NL(23)%RMOLMASS=72.1,
YCHEM_NL(23)%IGRBCODE=217023,
YCHEM_NL(23)%LNEGFIX=.TRUE.,
YCHEM_NL(23)%IGRIBTC=218023,
YCHEM_NL(23)%IGRIBDV=221023,
YCHEM_NL(23)%HENRYA=3.4e4,
YCHEM_NL(23)%HENRYB=7500.0,
YCHEM_NL(23)%LMASSFIX=.TRUE.,
YCHEM_NL(23)%BETAMFBC=-999.9,
YCHEM_NL(23)%LASSIM=.FALSE.,
YCHEM_NL(24)%CNAME='O3S',
YCHEM_NL(24)%NREQIN=1,
YCHEM_NL(24)%RMOLMASS=48.0,
YCHEM_NL(24)%IGRBCODE=217024,
YCHEM_NL(24)%LNEGFIX=.TRUE.,
YCHEM_NL(24)%IGRIBTC=218024,
YCHEM_NL(24)%IGRIBDV=221024,
YCHEM_NL(24)%HENRYA=1.0e-2,
YCHEM_NL(24)%HENRYB=2800.0,
YCHEM_NL(24)%LMASSFIX=.TRUE.,
YCHEM_NL(24)%BETAMFBC=-999.9,
YCHEM_NL(24)%LASSIM=.FALSE.,
YCHEM_NL(25)%CNAME='Rn',
YCHEM_NL(25)%NREQIN=1,
YCHEM_NL(25)%RMOLMASS=222.0,
YCHEM_NL(25)%IGRBCODE=210181,
YCHEM_NL(25)%LNEGFIX=.TRUE.,
YCHEM_NL(25)%IGRIBTC=210183,
YCHEM_NL(25)%LMASSFIX=.TRUE.,
YCHEM_NL(25)%BETAMFBC=-999.9,
YCHEM_NL(25)%LASSIM=.FALSE.,
YCHEM_NL(26)%CNAME='Pb',
YCHEM_NL(26)%NREQIN=1,
YCHEM_NL(26)%RMOLMASS=210.0,
YCHEM_NL(26)%IGRBCODE=217026,
YCHEM_NL(26)%LNEGFIX=.TRUE.,
YCHEM_NL(26)%IGRIBTC=218026,
YCHEM_NL(26)%HENRYA=3.2e11,
YCHEM_NL(26)%HENRYB=8700.0,
YCHEM_NL(26)%LMASSFIX=.TRUE.,
YCHEM_NL(26)%BETAMFBC=-999.9,
YCHEM_NL(26)%LASSIM=.FALSE.,
YCHEM_NL(27)%CNAME='NO',
YCHEM_NL(27)%NREQIN=1,
YCHEM_NL(27)%RMOLMASS=30.0,
YCHEM_NL(27)%IGRBCODE=217027,
YCHEM_NL(27)%LNEGFIX=.TRUE.,
YCHEM_NL(27)%IGRIBTC=218027,
YCHEM_NL(27)%IGRIBDV=221027,
YCHEM_NL(27)%HENRYA=1.9e-3,
YCHEM_NL(27)%HENRYB=1600.0,
YCHEM_NL(27)%LMASSFIX=.TRUE.,
YCHEM_NL(27)%BETAMFBC=-999.9,
YCHEM_NL(27)%LMASSFIX=.FALSE.,
YCHEM_NL(27)%LADV=.TRUE.,
YCHEM_NL(27)%LASSIM=.FALSE.,
YCHEM_NL(28)%CNAME='HO2',
YCHEM_NL(28)%NREQIN=1,
YCHEM_NL(28)%RMOLMASS=33.0,
YCHEM_NL(28)%IGRBCODE=217028,
YCHEM_NL(28)%LNEGFIX=.TRUE.,
YCHEM_NL(28)%IGRIBTC=218028,
YCHEM_NL(28)%IGRIBDV=221028,
YCHEM_NL(28)%HENRYA=6.8e2,
YCHEM_NL(28)%HENRYB=0.0,
YCHEM_NL(28)%LMASSFIX=.FALSE.,
YCHEM_NL(28)%LADV=.FALSE.,
YCHEM_NL(28)%LASSIM=.FALSE.,
YCHEM_NL(29)%CNAME='CH3O2',
YCHEM_NL(29)%NREQIN=1,
YCHEM_NL(29)%RMOLMASS=47.0
YCHEM_NL(29)%IGRBCODE=217029,
YCHEM_NL(29)%LNEGFIX=.TRUE.,
YCHEM_NL(29)%IGRIBTC=218029,
YCHEM_NL(29)%IGRIBDV=221029,
YCHEM_NL(29)%HENRYA=1.5e1,
YCHEM_NL(29)%HENRYB=3700.0,
YCHEM_NL(29)%LMASSFIX=.FALSE.,
YCHEM_NL(29)%LADV=.FALSE.,
YCHEM_NL(29)%LASSIM=.FALSE.,
YCHEM_NL(30)%CNAME='OH',
YCHEM_NL(30)%NREQIN=1,
YCHEM_NL(30)%RMOLMASS=17.0,
YCHEM_NL(30)%IGRBCODE=217030,
YCHEM_NL(30)%LNEGFIX=.TRUE.,
YCHEM_NL(30)%IGRIBTC=218030,
YCHEM_NL(30)%LMASSFIX=.FALSE.,
YCHEM_NL(30)%LADV=.FALSE.,
YCHEM_NL(30)%LASSIM=.FALSE.,
YCHEM_NL(31)%CNAME='NO2',
YCHEM_NL(31)%NREQIN=1,
YCHEM_NL(31)%RMOLMASS=46.0,
YCHEM_NL(31)%IGRBCODE=210121,
YCHEM_NL(31)%LNEGFIX=.TRUE.,
YCHEM_NL(31)%IGRIBTC=210125,
YCHEM_NL(31)%IGRIBDV=221031,
YCHEM_NL(31)%HENRYA=1.2e-2,
YCHEM_NL(31)%HENRYB=2400.0,
YCHEM_NL(31)%LMASSFIX=.TRUE.,
YCHEM_NL(31)%BETAMFBC=-999.9,
YCHEM_NL(31)%LMASSFIX=.FALSE.,
YCHEM_NL(31)%LADV=.TRUE.,
YCHEM_NL(31)%LASSIM=.TRUE.,
YCHEM_NL(31)%LADV5=.TRUE.,
YCHEM_NL(32)%CNAME='NO3',
YCHEM_NL(32)%NREQIN=1,
YCHEM_NL(32)%RMOLMASS=62.0,
YCHEM_NL(32)%IGRBCODE=217032,
YCHEM_NL(32)%LNEGFIX=.TRUE.,
YCHEM_NL(32)%IGRIBTC=218032,
YCHEM_NL(32)%IGRIBDV=221032,
YCHEM_NL(32)%HENRYA=3.8e-2,
YCHEM_NL(32)%HENRYB=0.,
YCHEM_NL(32)%LMASSFIX=.TRUE.,
YCHEM_NL(32)%BETAMFBC=-999.9,
YCHEM_NL(32)%LMASSFIX=.FALSE.,
YCHEM_NL(32)%LADV=.TRUE.,
YCHEM_NL(32)%LASSIM=.FALSE.,
YCHEM_NL(33)%CNAME='N2O5',
YCHEM_NL(33)%NREQIN=1,
YCHEM_NL(33)%RMOLMASS=108.0,
YCHEM_NL(33)%IGRBCODE=217033,
YCHEM_NL(33)%LNEGFIX=.TRUE.,
YCHEM_NL(33)%IGRIBTC=218033,
YCHEM_NL(33)%IGRIBDV=221033,
YCHEM_NL(33)%HENRYA=2.1e+1,
YCHEM_NL(33)%HENRYB=3400.0,
YCHEM_NL(33)%LMASSFIX=.TRUE.,
YCHEM_NL(33)%BETAMFBC=-999.9,
YCHEM_NL(33)%LASSIM=.FALSE.,
YCHEM_NL(34)%CNAME='HO2NO2',
YCHEM_NL(34)%NREQIN=1,
YCHEM_NL(34)%RMOLMASS=79.0,
YCHEM_NL(34)%IGRBCODE=217034,
YCHEM_NL(34)%LNEGFIX=.TRUE.,
YCHEM_NL(34)%IGRIBTC=218034,
YCHEM_NL(34)%IGRIBDV=221034,
YCHEM_NL(34)%HENRYA=1.2e+4,
YCHEM_NL(34)%HENRYB=6900.0,
YCHEM_NL(34)%LMASSFIX=.TRUE.,
YCHEM_NL(34)%BETAMFBC=-999.9,
YCHEM_NL(34)%LASSIM=.FALSE.,
YCHEM_NL(35)%CNAME='C2O3',
YCHEM_NL(35)%NREQIN=1,
YCHEM_NL(35)%RMOLMASS=75.0,
YCHEM_NL(35)%IGRBCODE=217035,
YCHEM_NL(35)%LNEGFIX=.TRUE.,
YCHEM_NL(35)%IGRIBTC=218035,
YCHEM_NL(35)%LMASSFIX=.FALSE.,
YCHEM_NL(35)%LADV=.FALSE.,
YCHEM_NL(35)%LASSIM=.FALSE.,
YCHEM_NL(36)%CNAME='ROR',
YCHEM_NL(36)%NREQIN=1,
YCHEM_NL(36)%RMOLMASS=28.0,
YCHEM_NL(36)%IGRBCODE=217036,
YCHEM_NL(36)%LNEGFIX=.TRUE.,
YCHEM_NL(36)%IGRIBTC=218036,
YCHEM_NL(36)%LMASSFIX=.FALSE.,
YCHEM_NL(36)%LADV=.FALSE.,
YCHEM_NL(36)%LASSIM=.FALSE.,
YCHEM_NL(37)%CNAME='RXPAR',
YCHEM_NL(37)%NREQIN=1,
YCHEM_NL(37)%RMOLMASS=12.0,
YCHEM_NL(37)%IGRBCODE=217037,
YCHEM_NL(37)%LNEGFIX=.TRUE.,
YCHEM_NL(37)%IGRIBTC=218037,
YCHEM_NL(37)%LMASSFIX=.FALSE.,
YCHEM_NL(37)%LADV=.FALSE.,
YCHEM_NL(37)%LASSIM=.FALSE.,
YCHEM_NL(38)%CNAME='XO2',
YCHEM_NL(38)%NREQIN=1,
YCHEM_NL(38)%RMOLMASS=44.0,
YCHEM_NL(38)%IGRBCODE=217038,
YCHEM_NL(38)%LNEGFIX=.TRUE.,
YCHEM_NL(38)%IGRIBTC=218038,
YCHEM_NL(38)%LMASSFIX=.FALSE.,
YCHEM_NL(38)%LADV=.FALSE.,
YCHEM_NL(38)%LASSIM=.FALSE.,
YCHEM_NL(39)%CNAME='XO2N',
YCHEM_NL(39)%NREQIN=1,
YCHEM_NL(39)%RMOLMASS=44.0,
YCHEM_NL(39)%IGRBCODE=217039,
YCHEM_NL(39)%LNEGFIX=.TRUE.,
YCHEM_NL(39)%IGRIBTC=218039,
YCHEM_NL(39)%LMASSFIX=.FALSE.,
YCHEM_NL(39)%LADV=.FALSE.,
YCHEM_NL(39)%LASSIM=.FALSE.,
YCHEM_NL(40)%CNAME='NH2',
YCHEM_NL(40)%NREQIN=1,
YCHEM_NL(40)%RMOLMASS=16.0,
YCHEM_NL(40)%IGRBCODE=217040,
YCHEM_NL(40)%LNEGFIX=.TRUE.,
YCHEM_NL(40)%IGRIBTC=218040,
YCHEM_NL(40)%LMASSFIX=.FALSE.,
YCHEM_NL(40)%LADV=.FALSE.,
YCHEM_NL(40)%LASSIM=.FALSE.,
YCHEM_NL(41)%CNAME='PSC',
YCHEM_NL(41)%NREQIN=0,
YCHEM_NL(41)%RMOLMASS=1.0,
YCHEM_NL(41)%IGRBCODE=217041,
YCHEM_NL(41)%LNEGFIX=.TRUE.,
YCHEM_NL(41)%IGRIBTC=218041,
YCHEM_NL(41)%LMASSFIX=.TRUE.,
YCHEM_NL(41)%BETAMFBC=-999.9,
YCHEM_NL(41)%LMASSFIX=.FALSE.,
YCHEM_NL(41)%REFVALI=0.0,
YCHEM_NL(41)%NREQIN=-1,
YCHEM_NL(41)%LADV=.FALSE.,
YCHEM_NL(41)%LASSIM=.FALSE.,
YCHEM_NL(42)%CNAME='CH3OH',
YCHEM_NL(42)%NREQIN=1,
YCHEM_NL(42)%RMOLMASS=31.01,
YCHEM_NL(42)%IGRBCODE=217042,
YCHEM_NL(42)%LNEGFIX=.TRUE.,
YCHEM_NL(42)%IGRIBTC=218042,
YCHEM_NL(42)%IGRIBDV=221042,
YCHEM_NL(42)%HENRYA=200.0,
YCHEM_NL(42)%HENRYB=5600.0,
YCHEM_NL(42)%LMASSFIX=.TRUE.,
YCHEM_NL(42)%BETAMFBC=-999.9,
YCHEM_NL(42)%LASSIM=.FALSE.,
YCHEM_NL(43)%CNAME='HCOOH',
YCHEM_NL(43)%NREQIN=1,
YCHEM_NL(43)%RMOLMASS=46.01,
YCHEM_NL(43)%IGRBCODE=217043,
YCHEM_NL(43)%LNEGFIX=.TRUE.,
YCHEM_NL(43)%IGRIBTC=218043,
YCHEM_NL(43)%IGRIBDV=221043,
YCHEM_NL(43)%HENRYA=8.8e3,
YCHEM_NL(43)%HENRYB=6100.0,
YCHEM_NL(43)%LMASSFIX=.TRUE.,
YCHEM_NL(43)%BETAMFBC=-999.9,
YCHEM_NL(43)%LASSIM=.FALSE.,
YCHEM_NL(44)%CNAME='MCOOH',
YCHEM_NL(44)%NREQIN=1,
YCHEM_NL(44)%RMOLMASS=62.02,
YCHEM_NL(44)%IGRBCODE=217044,
YCHEM_NL(44)%LNEGFIX=.TRUE.,
YCHEM_NL(44)%IGRIBTC=218044,
YCHEM_NL(44)%IGRIBDV=221044,
YCHEM_NL(44)%HENRYA=4.1e3,
YCHEM_NL(44)%HENRYB=6300.0,
YCHEM_NL(44)%LMASSFIX=.TRUE.,
YCHEM_NL(44)%BETAMFBC=-999.9,
YCHEM_NL(44)%LASSIM=.FALSE.,
YCHEM_NL(45)%CNAME='C2H6',
YCHEM_NL(45)%NREQIN=1,
YCHEM_NL(45)%RMOLMASS=30.02,
YCHEM_NL(45)%IGRBCODE=217045,
YCHEM_NL(45)%LNEGFIX=.TRUE.,
YCHEM_NL(45)%IGRIBTC=218045,
YCHEM_NL(45)%IGRIBDV=221045,
YCHEM_NL(45)%HENRYA=1.9e-3,
YCHEM_NL(45)%HENRYB=2400.0,
YCHEM_NL(45)%LMASSFIX=.TRUE.,
YCHEM_NL(45)%BETAMFBC=-999.9,
YCHEM_NL(45)%LASSIM=.FALSE.,
YCHEM_NL(46)%CNAME='C2H5OH',
YCHEM_NL(46)%NREQIN=1,
YCHEM_NL(46)%RMOLMASS=46.02,
YCHEM_NL(46)%IGRBCODE=217046,
YCHEM_NL(46)%LNEGFIX=.TRUE.,
YCHEM_NL(46)%IGRIBTC=218046,
YCHEM_NL(46)%IGRIBDV=221046,
YCHEM_NL(46)%HENRYA=190.0,
YCHEM_NL(46)%HENRYB=6400.0,
YCHEM_NL(46)%LMASSFIX=.TRUE.,
YCHEM_NL(46)%BETAMFBC=-999.9,
YCHEM_NL(46)%LASSIM=.FALSE.,
YCHEM_NL(47)%CNAME='C3H8',
YCHEM_NL(47)%NREQIN=1,
YCHEM_NL(47)%RMOLMASS=44.03,
YCHEM_NL(47)%IGRBCODE=217047,
YCHEM_NL(47)%LNEGFIX=.TRUE.,
YCHEM_NL(47)%IGRIBTC=218047,
YCHEM_NL(47)%LMASSFIX=.TRUE.,
YCHEM_NL(47)%BETAMFBC=-999.9,
YCHEM_NL(47)%LASSIM=.FALSE.,
YCHEM_NL(48)%CNAME='C3H6',
YCHEM_NL(48)%NREQIN=1,
YCHEM_NL(48)%RMOLMASS=42.03,
YCHEM_NL(48)%IGRBCODE=217048,
YCHEM_NL(48)%LNEGFIX=.TRUE.,
YCHEM_NL(48)%IGRIBTC=218048,
YCHEM_NL(48)%LMASSFIX=.TRUE.,
YCHEM_NL(48)%BETAMFBC=-999.9,
YCHEM_NL(48)%LASSIM=.FALSE.,
YCHEM_NL(49)%CNAME='C10H16',
YCHEM_NL(49)%NREQIN=1,
YCHEM_NL(49)%RMOLMASS=136.0,
YCHEM_NL(49)%IGRBCODE=217049,
YCHEM_NL(49)%LNEGFIX=.TRUE.,
YCHEM_NL(49)%IGRIBTC=218049,
YCHEM_NL(49)%LMASSFIX=.TRUE.,
YCHEM_NL(49)%BETAMFBC=-999.9,
YCHEM_NL(49)%LASSIM=.FALSE.,
YCHEM_NL(50)%CNAME='ISPD',
YCHEM_NL(50)%NREQIN=1,
YCHEM_NL(50)%RMOLMASS=70.0,
YCHEM_NL(50)%IGRBCODE=217050,
YCHEM_NL(50)%LNEGFIX=.TRUE.,
YCHEM_NL(50)%IGRIBTC=218050,
YCHEM_NL(50)%IGRIBDV=221050,
YCHEM_NL(50)%HENRYA=4.3,
YCHEM_NL(50)%HENRYB=5300.0,
YCHEM_NL(50)%LMASSFIX=.TRUE.,
YCHEM_NL(50)%BETAMFBC=-999.9,
YCHEM_NL(50)%LASSIM=.FALSE.,
YCHEM_NL(51)%CNAME='NO3_A',
YCHEM_NL(51)%NREQIN=1,
YCHEM_NL(51)%RMOLMASS=62.0,
YCHEM_NL(51)%IGRBCODE=217051,
YCHEM_NL(51)%LNEGFIX=.TRUE.,
YCHEM_NL(51)%IGRIBTC=218051,
YCHEM_NL(51)%IGRIBDV=221051,
YCHEM_NL(51)%HENRYA=3.2e11,
YCHEM_NL(51)%HENRYB=8700.0,
YCHEM_NL(51)%LMASSFIX=.TRUE.,
YCHEM_NL(51)%BETAMFBC=-999.9,
YCHEM_NL(51)%LASSIM=.FALSE.,
YCHEM_NL(52)%CNAME='CH3COCH3',
YCHEM_NL(52)%NREQIN=0,
YCHEM_NL(52)%RMOLMASS=58.0,
YCHEM_NL(52)%IGRBCODE=217052,
YCHEM_NL(52)%LNEGFIX=.TRUE.,
YCHEM_NL(52)%IGRIBTC=218052,
YCHEM_NL(52)%IGRIBDV=221052,
YCHEM_NL(52)%HENRYA=27.0,
YCHEM_NL(52)%HENRYB=5500.0,
YCHEM_NL(52)%LMASSFIX=.TRUE.,
YCHEM_NL(52)%BETAMFBC=-999.9,
YCHEM_NL(52)%LASSIM=.FALSE.,
YCHEM_NL(53)%CNAME='ACO2',
YCHEM_NL(53)%NREQIN=0,
YCHEM_NL(53)%RMOLMASS=58.0,
YCHEM_NL(53)%IGRBCODE=217053,
YCHEM_NL(53)%LNEGFIX=.TRUE.,
YCHEM_NL(53)%IGRIBTC=218053,
YCHEM_NL(53)%LMASSFIX=.FALSE.,
YCHEM_NL(53)%LADV=.FALSE.,
YCHEM_NL(53)%LASSIM=.FALSE.,
YCHEM_NL(54)%CNAME='IC3H7O2',
YCHEM_NL(54)%NREQIN=0,
YCHEM_NL(54)%RMOLMASS=75.0,
YCHEM_NL(54)%IGRBCODE=217054,
YCHEM_NL(54)%LNEGFIX=.TRUE.,
YCHEM_NL(54)%IGRIBTC=218054,
YCHEM_NL(54)%LMASSFIX=.FALSE.,
YCHEM_NL(54)%LADV=.FALSE.,
YCHEM_NL(54)%LASSIM=.FALSE.,
YCHEM_NL(55)%CNAME='HYPROPO2',
YCHEM_NL(55)%NREQIN=0,
YCHEM_NL(55)%RMOLMASS=91.0,
YCHEM_NL(55)%IGRBCODE=217055,
YCHEM_NL(55)%LNEGFIX=.TRUE.,
YCHEM_NL(55)%IGRIBTC=218055,
YCHEM_NL(55)%LMASSFIX=.FALSE.,
YCHEM_NL(55)%LADV=.FALSE.,
YCHEM_NL(55)%LASSIM=.FALSE.,
YCHEM_NL(56)%CNAME='SO3',
YCHEM_NL(56)%NREQIN=0,
YCHEM_NL(56)%RMOLMASS=80.0,
YCHEM_NL(56)%IGRBCODE=217189,
YCHEM_NL(56)%LNEGFIX=.TRUE.,
YCHEM_NL(56)%IGRIBTC=218189,
YCHEM_NL(56)%LMASSFIX=.TRUE.,
YCHEM_NL(56)%BETAMFBC=-999.9,
YCHEM_NL(56)%LASSIM=.FALSE.,
YCHEM_NL(57)%CNAME='NOXA',
YCHEM_NL(57)%NREQIN=0,
YCHEM_NL(57)%RMOLMASS=46.0,
YCHEM_NL(57)%IGRBCODE=217056,
YCHEM_NL(57)%LNEGFIX=.TRUE.,
YCHEM_NL(57)%IGRIBTC=218056,
YCHEM_NL(57)%LMASSFIX=.TRUE.,
YCHEM_NL(57)%BETAMFBC=-999.9,
YCHEM_NL(57)%LASSIM=.FALSE.,
YEXT_NL(1)%CNAME='EMILI',
YEXT_NL(1)%NREQIN=0,
YEXT_NL(1)%IGRBCODE=212254,
YEXT_NL(1)%LADV=.FALSE.,
YEXT_NL(1)%LGP=.TRUE.,
YEXT_NL(1)%LGPINGP=.TRUE.,
YEXT_NL(2)%CNAME='DDVEL',
YEXT_NL(2)%NREQIN=0,
YEXT_NL(2)%IGRBCODE=212250,
YEXT_NL(2)%LADV=.FALSE.,
YEXT_NL(2)%LGP=.TRUE.,
YEXT_NL(2)%LGPINGP=.TRUE.,
YEXT_NL(3)%CNAME='DDFLXA',
YEXT_NL(3)%NREQIN=0,
YEXT_NL(3)%IGRBCODE=212251,
YEXT_NL(3)%LADV=.FALSE.,
YEXT_NL(3)%LGP=.TRUE.,
YEXT_NL(3)%LGPINGP=.TRUE.,
YEXT_NL(4)%CNAME='WDFLXA',
YEXT_NL(4)%NREQIN=0,
YEXT_NL(4)%IGRBCODE=212252,
YEXT_NL(4)%LADV=.FALSE.,
YEXT_NL(4)%LGP=.TRUE.,
YEXT_NL(4)%LGPINGP=.TRUE.,
NGFL_EXT=4,
LAERCHEM=.TRUE.
YQ_NL%LGP=.TRUE.,
YQ_NL%LSP=.FALSE.,
YO3_NL%LGP=.TRUE.,
YQ_NL%LGPINGP=.TRUE.,
YL_NL%LGP=.TRUE.,
YI_NL%LGP=.TRUE.,
YA_NL%LGP=.TRUE.,
/
&NAERAD
LECOMPGRID=.FALSE.,
RMUZUV=0.01,
NUVTIM=72,
NRADUV=1,
NUV=24,
LUVPROC=.TRUE.,
LUVAERP=.TRUE.,
KMODTS=2,
NRPROMA=0,
LEPO3RA=.TRUE.,
LO3_CHEM_UV=.TRUE.,
NGHGRAD=20,
NAERMACC=0,
LAER3D=.FALSE.,
/
&NAMFPC
CFPFMT="MODEL",
NFP3DFS=5,
NFP3DFP=3,
MFP3DFS(:)=210001,210022,210048,210203,210061
MFP3DFP(:)=210011,217003,210062
NFP2DF=1,
MFP2DF(:)=152,
NFPPHY=25,
MFPPHY(:)=210031,210072,210073,210074,210119,215022,215020,210208,210052,214002,210207,215104,210217,215099,215120,215143,215179,215028,215032,215055,215058,215194,210206,215089,210064,
NRFP3S(:)=-99,
RFP3P=100000.,85000.,70000.,50000.,
/
&NAEAER
NTYPAER=3,3,2,2,1,2,1,0,0,0,
YAERO_DESC(1)%IGRIBDIAG=215001,215004,215007,215010,215013,215016,215022,215019,
YAERO_DESC(1)%RDDEPVSEA=0.011,
YAERO_DESC(1)%RDDEPVLIC=0.011,
YAERO_DESC(1)%RSEDIMV=0.,
YAERO_DESC(1)%RSCAVIN=0.9,
YAERO_DESC(1)%RSCAVBCR=0.001,
YAERO_DESC(1)%RSCAVBCS=0.01,
YAERO_DESC(1)%COPTCLASS='SeaSaltSmall',
YAERO_DESC(1)%CHYGCLASS='SeaSalt',
YAERO_DESC(1)%IAEROCV=1,
YAERO_DESC(2)%IGRIBDIAG=215002,215005,215008,215011,215014,215017,215023,215020,
YAERO_DESC(2)%RDDEPVSEA=0.0115,
YAERO_DESC(2)%RDDEPVLIC=0.012,
YAERO_DESC(2)%RSEDIMV=0.,
YAERO_DESC(2)%RSCAVIN=0.9,
YAERO_DESC(2)%RSCAVBCR=0.001,
YAERO_DESC(2)%RSCAVBCS=0.01,
YAERO_DESC(2)%COPTCLASS='SeaSaltMedium',
YAERO_DESC(2)%CHYGCLASS='SeaSalt',
YAERO_DESC(2)%IAEROCV=2,
YAERO_DESC(3)%IGRIBDIAG=215003,215006,215009,215012,215015,215018,215024,215021,
YAERO_DESC(3)%RDDEPVSEA=0.012,
YAERO_DESC(3)%RDDEPVLIC=0.015,
YAERO_DESC(3)%RSEDIMV=0.018,
YAERO_DESC(3)%RSCAVIN=0.9,
YAERO_DESC(3)%RSCAVBCR=0.001,
YAERO_DESC(3)%RSCAVBCS=0.01,
YAERO_DESC(3)%COPTCLASS='SeaSaltLarge',
YAERO_DESC(3)%CHYGCLASS='SeaSalt',
YAERO_DESC(3)%IAEROCV=2,
YAERO_DESC(4)%IGRIBDIAG=215025,215028,215031,215034,215037,215040,215046,215043,
YAERO_DESC(4)%RDDEPVSEA=0.0002,
YAERO_DESC(4)%RDDEPVLIC=0.0002,
YAERO_DESC(4)%RSEDIMV=0.,
YAERO_DESC(4)%RSCAVIN=0.7,
YAERO_DESC(4)%RSCAVBCR=0.001,
YAERO_DESC(4)%RSCAVBCS=0.01,
YAERO_DESC(4)%COPTCLASS='DustSmall',
YAERO_DESC(4)%CHYGCLASS='Dust',
YAERO_DESC(4)%IAEROCV=1,
YAERO_DESC(5)%IGRIBDIAG=215026,215029,215032,215035,215038,215041,215047,215044,
YAERO_DESC(5)%RDDEPVSEA=0.001,
YAERO_DESC(5)%RDDEPVLIC=0.001,
YAERO_DESC(5)%RSEDIMV=0.,
YAERO_DESC(5)%RSCAVIN=0.7,
YAERO_DESC(5)%RSCAVBCR=0.001,
YAERO_DESC(5)%RSCAVBCS=0.01,
YAERO_DESC(5)%COPTCLASS='DustMedium',
YAERO_DESC(5)%CHYGCLASS='Dust',
YAERO_DESC(5)%IAEROCV=2,
YAERO_DESC(6)%IGRIBDIAG=215027,215030,215033,215036,215039,215042,215048,215045,
YAERO_DESC(6)%RDDEPVSEA=0.012,
YAERO_DESC(6)%RDDEPVLIC=0.012,
YAERO_DESC(6)%RSEDIMV=0.001962,
YAERO_DESC(6)%RSCAVIN=0.7,
YAERO_DESC(6)%RSCAVBCR=0.001,
YAERO_DESC(6)%RSCAVBCS=0.01,
YAERO_DESC(6)%COPTCLASS='DustLarge',
YAERO_DESC(6)%CHYGCLASS='Dust',
YAERO_DESC(6)%IAEROCV=2,
YAERO_DESC(7)%IGRIBDIAG=215049,215051,215053,215055,215057,215059,215063,215061,
YAERO_DESC(7)%RDDEPVSEA=0.001,
YAERO_DESC(7)%RDDEPVLIC=0.001,
YAERO_DESC(7)%RSEDIMV=0.,
YAERO_DESC(7)%RSCAVIN=0.7,
YAERO_DESC(7)%RSCAVBCR=0.001,
YAERO_DESC(7)%RSCAVBCS=0.01,
YAERO_DESC(7)%COPTCLASS='OrganicMatter',
YAERO_DESC(7)%CHYGCLASS='OrganicMatter',
YAERO_DESC(7)%IAEROCV=1,
YAERO_DESC(8)%IGRIBDIAG=215050,215052,215054,215056,215058,215060,215064,215062,
YAERO_DESC(8)%RDDEPVSEA=0.001,
YAERO_DESC(8)%RDDEPVLIC=0.001,
YAERO_DESC(8)%RSEDIMV=0.,
YAERO_DESC(8)%RSCAVIN=0,
YAERO_DESC(8)%RSCAVBCR=0.001,
YAERO_DESC(8)%RSCAVBCS=0.01,
YAERO_DESC(8)%COPTCLASS='OrganicMatter',
YAERO_DESC(8)%CHYGCLASS='Hydrophobic',
YAERO_DESC(8)%IAEROCV=1,
YAERO_DESC(9)%IGRIBDIAG=215065,215067,215069,215071,215073,215075,215079,215077,
YAERO_DESC(9)%RDDEPVSEA=0.001,
YAERO_DESC(9)%RDDEPVLIC=0.001,
YAERO_DESC(9)%RSEDIMV=0.,
YAERO_DESC(9)%RSCAVIN=0.7,
YAERO_DESC(9)%RSCAVBCR=0.001,
YAERO_DESC(9)%RSCAVBCS=0.01,
YAERO_DESC(9)%COPTCLASS='BlackCarbon',
YAERO_DESC(9)%CHYGCLASS='OrganicMatter',
YAERO_DESC(9)%IAEROCV=1,
YAERO_DESC(10)%IGRIBDIAG=215066,215068,215070,215072,215074,215076,215080,215078,
YAERO_DESC(10)%RDDEPVSEA=0.001,
YAERO_DESC(10)%RDDEPVLIC=0.001,
YAERO_DESC(10)%RSEDIMV=0.,
YAERO_DESC(10)%RSCAVIN=0,
YAERO_DESC(10)%RSCAVBCR=0.001,
YAERO_DESC(10)%RSCAVBCS=0.01,
YAERO_DESC(10)%COPTCLASS='BlackCarbon',
YAERO_DESC(10)%CHYGCLASS='Hydrophobic',
YAERO_DESC(10)%IAEROCV=1,
YAERO_DESC(11)%IGRIBDIAG=215081,215082,215083,215084,215085,215086,215088,215087,
YAERO_DESC(11)%RDDEPVSEA=0.0015,
YAERO_DESC(11)%RDDEPVLIC=0.0025,
YAERO_DESC(11)%RSEDIMV=0.,
YAERO_DESC(11)%RSCAVIN=0.7,
YAERO_DESC(11)%RSCAVBCR=0.001,
YAERO_DESC(11)%RSCAVBCS=0.01,
YAERO_DESC(11)%COPTCLASS='Sulphate',
YAERO_DESC(11)%CHYGCLASS='Sulphate',
YAERO_DESC(11)%IAEROCV=1,
YAERO_DESC(12)%IGRIBDIAG=215189,215191,215193,215195,215197,215199,215203,215201,
YAERO_DESC(12)%RDDEPVSEA=0.0015,
YAERO_DESC(12)%RDDEPVLIC=0.0015,
YAERO_DESC(12)%RSEDIMV=0.,
YAERO_DESC(12)%RSCAVIN=0.4,
YAERO_DESC(12)%RSCAVBCR=0.001,
YAERO_DESC(12)%RSCAVBCS=0.01,
YAERO_DESC(12)%COPTCLASS='NitrateFine',
YAERO_DESC(12)%CHYGCLASS='Nitrate',
YAERO_DESC(12)%IAEROCV=1,
YAERO_DESC(13)%IGRIBDIAG=215190,215192,215194,215196,215198,215200,215204,215202,
YAERO_DESC(13)%RDDEPVSEA=0.0015,
YAERO_DESC(13)%RDDEPVLIC=0.0015,
YAERO_DESC(13)%RSEDIMV=0.0013352,
YAERO_DESC(13)%RSCAVIN=0.8,
YAERO_DESC(13)%RSCAVBCR=0.001,
YAERO_DESC(13)%RSCAVBCS=0.01,
YAERO_DESC(13)%COPTCLASS='NitrateCoarse',
YAERO_DESC(13)%CHYGCLASS='Nitrate',
YAERO_DESC(13)%IAEROCV=2,
YAERO_DESC(14)%IGRIBDIAG=215205,215206,215207,215208,215209,215210,215212,215211,
YAERO_DESC(14)%RDDEPVSEA=0.0015,
YAERO_DESC(14)%RDDEPVLIC=0.0015,
YAERO_DESC(14)%RSEDIMV=0.,
YAERO_DESC(14)%RSCAVIN=0.8,
YAERO_DESC(14)%RSCAVBCR=0.001,
YAERO_DESC(14)%RSCAVBCS=0.01,
YAERO_DESC(14)%COPTCLASS='Ammonium',
YAERO_DESC(14)%CHYGCLASS='Ammonium',
YAERO_DESC(14)%IAEROCV=1,
LAERVOL=.FALSE.,
NDRYDEPVEL_DYN=1,
LAERNGAT=.TRUE.,
NAERSCAV=2,
LAERGTOP=.TRUE.,
LAERHYGRO=.TRUE.,
LAERSEDIM=.TRUE.,
LAERDRYDP=.TRUE.,
LAERSURF=.TRUE.,
NDDUST=2,
NSSALT=3,
NSO4SCHEME=1,
LSEASALT_RH80=.TRUE.,
LAERRRTM=.TRUE.,
LAERDUSTSOURCE=.TRUE.,
/
&NAMCOMPO
LCHEM_DIA=.TRUE.,
LCHEM_DDFLX=.TRUE.,
LCOMPO_DDFLX_DIR=.TRUE.,
LCHEM_TROPO=.TRUE.,
LCOMPO_DCDD=.TRUE.,
LAERNITRATE=.TRUE.,
/
&NAMCHEM
CHEM_SCHEME="tm5",
LCHEM_DIAC=.FALSE.,
LCHEM_LIGHT=.TRUE.,
LCHEM_0NOX=.FALSE.,
KCHEM_NOXADV=0,
LCHEM_ANAO3=.FALSE.,
KCHEM_SOLVE=1,
LCHEM_JOUT=.FALSE.,
LCHEM_AEROI=.TRUE.,
LCHEM_O3RAD=.TRUE.,
LCHEM_CSHAPE=.FALSE.,
LCHEM_WDFR=.TRUE.,
LCHEM_REVCHEM=.FALSE.,
KCHEM_DRYDEP=1,
/
&NAMMCC
LMCCEC=.TRUE.,
LMCCIEC=.FALSE.,
LMCC_COMPO=.FALSE.,
/
&NAMCOMPO_EMIS THIS%SPECIES='CO2_GHG', THIS%SECTOR='Ocean', THIS%PARAMID=210067, THIS%SCALING=-1.0 /
&NAMCOMPO_EMIS THIS%SPECIES='CO2_GHG', THIS%SECTOR='Anthropogenic', THIS%PARAMID=210069, THIS%SCALING=-1.0 /
&NAMCOMPO_EMIS THIS%SPECIES='CH4_GHG', THIS%SECTOR='AllButBiomassBur', THIS%PARAMID=210070, THIS%SCALING=-1.0 /
&NAMCOMPO_EMIS THIS%SPECIES='CO2_GHG', THIS%SECTOR='Biomass-burning', THIS%PARAMID=210080, THIS%DIURNAL_CYCLE_TYPE='GFAS', THIS%DIURNAL_PEAK_HOUR=13.5, THIS%DIURNAL_BASELINE=0.2 /
&NAMCOMPO_EMIS THIS%SPECIES='CH4_GHG', THIS%SECTOR='Biomass-burning', THIS%PARAMID=210082, THIS%DIURNAL_CYCLE_TYPE='GFAS', THIS%DIURNAL_PEAK_HOUR=13.5, THIS%DIURNAL_BASELINE=0.2 /
&NAMCOMPO_EMIS THIS%SPECIES='CO2_GHG', THIS%SECTOR='Aviation', THIS%PARAMID=212253, THIS%VERTICAL_PROFILE_TYPE='3D' /
&NAMCOMPO_EMIS THIS%SPECIES='CO', THIS%SECTOR='All', THIS%PARAMID=219005, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='CH2O', THIS%SECTOR='All', THIS%PARAMID=219008, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.25, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='PAR', THIS%SECTOR='All', THIS%PARAMID=219009, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.65, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='C2H4', THIS%SECTOR='All', THIS%PARAMID=219010, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.25, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='OLE', THIS%SECTOR='All', THIS%PARAMID=219011, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.65, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='ALD2', THIS%SECTOR='All', THIS%PARAMID=219012, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.25, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='C5H8', THIS%SECTOR='All', THIS%PARAMID=219016, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.0, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='SO2', THIS%SECTOR='All', THIS%PARAMID=219017, THIS%VERTICAL_PROFILE_TYPE="AltitudeMap", THIS%VERTICAL_PARAMID=216045, THIS%VERTICAL_BASE_LEVEL=-1, THIS%VERTICAL_TOP_LEVEL=-4, THIS%VERTICAL_THRESHOLD=200, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='DMS', THIS%SECTOR='All', THIS%PARAMID=219018, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='NH3', THIS%SECTOR='All', THIS%PARAMID=219019, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='CH3COCHO', THIS%SECTOR='All', THIS%PARAMID=219023, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='Rn', THIS%SECTOR='All', THIS%PARAMID=219025, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='NO', THIS%SECTOR='AllButAviation', THIS%PARAMID=219027, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='CH3OH', THIS%SECTOR='All', THIS%PARAMID=219042, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.25, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='HCOOH', THIS%SECTOR='All', THIS%PARAMID=219043, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.25, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='MCOOH', THIS%SECTOR='All', THIS%PARAMID=219044, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.25, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='C2H6', THIS%SECTOR='All', THIS%PARAMID=219045, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.65, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='C2H5OH', THIS%SECTOR='All', THIS%PARAMID=219046, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.25, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='C3H8', THIS%SECTOR='All', THIS%PARAMID=219047, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.65, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='C3H6', THIS%SECTOR='All', THIS%PARAMID=219048, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.65, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='C10H16', THIS%SECTOR='All', THIS%PARAMID=219049, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.65, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='ISPD', THIS%SECTOR='All', THIS%PARAMID=219050, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.65, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='CH3COCH3', THIS%SECTOR='All', THIS%PARAMID=219052, THIS%DIURNAL_CYCLE_TYPE='VOC', THIS%DIURNAL_PEAK_HOUR=14.0, THIS%DIURNAL_BASELINE=0.65, THIS%LEGACY_CHEM_OVERRIDE=1 /
&NAMCOMPO_EMIS THIS%SPECIES='NO2', THIS%SECTOR='Aviation', THIS%PARAMID=212255, THIS%SCALING=1.5333333333333333333, THIS%VERTICAL_PROFILE_TYPE='3D' /
&NAMCOMPO_EMIS THIS%SPECIES='Black-carbon_A', THIS%SECTOR='Biogenic/biofuel', THIS%PARAMID=210031, THIS%SCALING=0.2 /
&NAMCOMPO_EMIS THIS%SPECIES='Black-carbon_B', THIS%SECTOR='Biogenic/biofuel', THIS%PARAMID=210031, THIS%SCALING=0.8 /
&NAMCOMPO_EMIS THIS%SPECIES='Black-carbon_A', THIS%SECTOR='Fossil-fuel', THIS%PARAMID=210032, THIS%SCALING=0.2 /
&NAMCOMPO_EMIS THIS%SPECIES='Black-carbon_B', THIS%SECTOR='Fossil-fuel', THIS%PARAMID=210032, THIS%SCALING=0.8 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_A', THIS%SECTOR='Biogenic/biofuel', THIS%PARAMID=210033, THIS%SCALING=0.5 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_B', THIS%SECTOR='Biogenic/biofuel', THIS%PARAMID=210033, THIS%SCALING=0.5 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_A', THIS%SECTOR='Fossil-fuel', THIS%PARAMID=210034, THIS%SCALING=0.5 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_B', THIS%SECTOR='Fossil-fuel', THIS%PARAMID=210034, THIS%SCALING=0.5 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_A', THIS%SECTOR='BiogenicSecondary', THIS%PARAMID=210039, THIS%SCALING=0.5 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_B', THIS%SECTOR='BiogenicSecondary', THIS%PARAMID=210039, THIS%SCALING=0.5 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_A', THIS%SECTOR='AnthroSecondary', THIS%PARAMID=216007, THIS%SCALING=0.5, THIS%DIURNAL_CYCLE_TYPE='GFAS', THIS%DIURNAL_PEAK_HOUR=12.0, THIS%DIURNAL_BASELINE=0.2 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_B', THIS%SECTOR='AnthroSecondary', THIS%PARAMID=216007, THIS%SCALING=0.5, THIS%DIURNAL_CYCLE_TYPE='GFAS', THIS%DIURNAL_PEAK_HOUR=12.0, THIS%DIURNAL_BASELINE=0.2 /
&NAMCOMPO_EMIS THIS%SPECIES='Black-carbon_A', THIS%SECTOR='Biomass-burning', THIS%PARAMID=210040, THIS%SCALING=0.2, THIS%DIURNAL_CYCLE_TYPE='GFAS', THIS%DIURNAL_PEAK_HOUR=13.5, THIS%DIURNAL_BASELINE=0.2 /
&NAMCOMPO_EMIS THIS%SPECIES='Black-carbon_B', THIS%SECTOR='Biomass-burning', THIS%PARAMID=210040, THIS%SCALING=0.8, THIS%DIURNAL_CYCLE_TYPE='GFAS', THIS%DIURNAL_PEAK_HOUR=13.5, THIS%DIURNAL_BASELINE=0.2 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_A', THIS%SECTOR='Biomass-burning', THIS%PARAMID=210041, THIS%SCALING=0.5, THIS%DIURNAL_CYCLE_TYPE='GFAS', THIS%DIURNAL_PEAK_HOUR=13.5, THIS%DIURNAL_BASELINE=0.2, THIS%LEGACY_CHEM_OVERRIDE=16 /
&NAMCOMPO_EMIS THIS%SPECIES='Organic-matter_B', THIS%SECTOR='Biomass-burning', THIS%PARAMID=210041, THIS%SCALING=0.5, THIS%DIURNAL_CYCLE_TYPE='GFAS', THIS%DIURNAL_PEAK_HOUR=13.5, THIS%DIURNAL_BASELINE=0.2 /
&NAMCOMPO_EMIS /
EOF

# Link, don't move, so we inherit ${NAMELIST_INIT} via ifs-run
# Add empty defaults for this cycle
cat ${NAMELIST_INIT} >> namelist_model2


TSTEP=3600
BASETIME=2019080100
tstep_json=$(echo $TSTEP| sed -e "s/\..//")
yyyy=$( echo $BASETIME | cut -c1-4 )
mm=$( echo $BASETIME | cut -c5-6 )
dd=$( echo $BASETIME | cut -c7-8 )
hh=$( echo $BASETIME | cut -c9-10 )
isobegin=${yyyy}-${mm}-${dd}T${hh}:00:00Z
echo "isobegin is $isobegin"


# Define geometries
cat > namelist_geom1 << EOF
&NAMDIM
NPROMA=-24,
NSMAX=42,
NMSMAX=42,
NVARMAX=42,
NDGLG=64,
NDLON=128,
NFLEVG=19,
/
EOF

cat > namelist_geom2 << EOF
&NAMDIM
NPROMA=-24,
NSMAX=21,
NMSMAX=21,
NVARMAX=21,
NDGLG=32,
NDLON=64,
NFLEVG=19,
/
EOF

LEVELS=19
cat namelist_geom_part ifsdata/vtable_L${LEVELS}  >> namelist_geom1
cat namelist_geom_part ifsdata/vtable_L${LEVELS}  >> namelist_geom2

# Create namelist for static part of setup (fort.4)
rm -f fort.4
namelists=(NAMPAR0 NAMPAR1 NAMCT0 NAMARG)
for namelist in ${namelists[@]}; do
  awk "/^\s*&$namelist\s*$/{flag=1}/^\s*\//{if (flag) {print;exit}}flag" common_namelists >> fort.4
done
#Empty defaults for this cycle will be added in ifs-run

# Create Json file

EXPVER=hmec
EXPVER1=hafb
EXPVER2=$EXPVER

cat > create_config_file.json <<EOF
{
   "expver":"${EXPVER}",
   "logging":{
      "categories":"Debug, Info, Stats, Test, Warning, Error, Trace"
   },
  "resolution": {
    "resolution1": {
      "namelist":"namelist_geom1",
      "orogfile":"ICMSH${EXPVER1}INIT"

    },
    "resolution2": {
      "namelist":"namelist_geom2",
      "orogfile":"ICMSH${EXPVER2}INIT"
    }
  },
  "model": {
    "model1": {
     "tstep":"PT1800S",
     "namelist":"namelist_model1",
     "forcing":{
       "variables":[]
     }
    },
    "model2": {
     "tstep":"PT${tstep_json}S",
     "namelist":"namelist_model2",
     "forcing":{
       "variables":["t","u","v","q","o3","sp"]
     }
    }
  },
  "initial": {
    "state1": {
                  "variables":[],
                  "ifile":0,
                  "date":"${isobegin}",
                  "expver":"${EXPVER1}"
    },
    "state2": {
                  "variables":[],
                  "ifile":0,
                  "date":"${isobegin}",
                  "expver":"${EXPVER2}"

    }
  },
  "forecast_length": "PT6H",
  "output": {
    "output1": {
       "namelist":"namelist_fp",
      "first":"P5D",
      "frequency":"PT12H",
      "type":"fc",
      "anoffset":"PT9H"

    },
    "output2": {
      "namelist":"namelist_fp",
      "first":"P5D",
      "frequency":"PT12H",
      "type":"fc",
      "anoffset":"PT9H"
    }
  },"prints": {
    "prints1": {
      "frequency": "PT3H"
    },
    "prints2": {
      "frequency": "PT3H"
    }
  }
}

EOF


export IFS_EXECUTABLE=ifs_DualConfigforecast.x
export IFS_ARGS=create_config_file.json
# File for extracting GFL infor in ifs-check-tracers
GFL_NAMELIST_FILE=namelist_model2

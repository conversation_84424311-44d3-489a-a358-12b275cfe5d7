README
======

Use of OpenIFS with Docker Containers
-------------------------------------

This directory contains scripts (Dockerfiles) to generate Ubuntu-based 
Docker container images of OpenIFS.

A basic description of their usage is available on the ECMWF OpenIFS web portal:  
https://confluence.ecmwf.int/display/OIFS/How+to+run+OpenIFS+in+a+Docker+container

The scripts in this directory are intended as examples and starting point for 
OS-level virtualisation and they are provided "as is" without further support.

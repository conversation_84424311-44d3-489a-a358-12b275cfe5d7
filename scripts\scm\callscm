#===============================================================================
# Script to call IFS SCM (run.scm)                  (<PERSON>, March 2020)
# to efficiently loop over executables, cases and timesteps
#
# To run the SCM: 
#
# Default with no arguments:
#  run.scm 
#  will assume expt_name="ref", namelist="namelist", scm_in="scm_in.nc" 
#  (the latter two must be in the current scm-run directory)
#
# With options:
#  run.scm [-c case_name] [-d duration] [-L nlevs] [-n namelist] [-o outdir] [-s scm_in] [-t timestep] [-v] [-x expt_name] [-X exec]
#   -c case_name name of the case study used for namelist and output directory
#   -d duration  duration of run in hours (default 72h)
#   -L nlevs     number of levels (default 137)
#   -n namelist  namelist file (default namelist.case_study if case_study defined, otherwise ./namelist in current directory
#   -o outdir    working/output subdirectory (default current directory)
#   -s scm_in    SCM input/forcing netcdf file (defaults ./scm_in.nc in current directory)
#   -t timestep  timestep in seconds (default 450s)
#   -v           verbose turns on set -x (default off)
#   -x expt_name shortname to identify experiment
#   -X exec      executable path and filename
#
#===============================================================================
set -e

log_echo ()
{
  builtin echo "$@" | tee -a "$SCM_LOGFILE"
}

usage_info()
{
  log_echo "----------------------------------------------------------------"
  log_echo "usage_info in callscm called because" "$1"
  log_echo "----------------------------------------------------------------"
  log_echo "callscm is a wrapper for a wrapper script to call IFS SCM (run.scm) to"
  log_echo "efficiently loop over cases and timesteps"
  log_echo ""
  log_echo "callscm usage :"
  log_echo "callscm -h -c <case_name or list of case_names> -t <timestep or list of timesteps>"
  log_echo "        -x <expt_name>"
  log_echo "where :"
  log_echo "-h is help which returns basic usage options and exits"
  log_echo "-c case_name or list of case_names (space delimited) of the case study" 
  log_echo "   used for namelist and output directory. Default list is"
  log_echo "   \"DYCOMS BOMEX TWPICE\" "
  log_echo "-t timestep or list of timesteps in seconds. The default is 450s. An" 
  log_echo "   example of a list is \"1800 900 300\" "
  log_echo "-x expt_name shortname to identify experiment. Default is ref-oifs-scm"
  log_echo "----------------------------------------------------------------"
}

#-------------------------------------------------------------------------------
# Check for the SCM_LOGFILE and remove if it exists
#-------------------------------------------------------------------------------
if [ -e "${SCM_LOGFILE-}" ]; then 
  rm "${SCM_LOGFILE}" # remove logfile so not adding to old logs
fi

#-------------------------------------------------------------------------------
# Set SCM code executable path (EXEC). This is defined in the oifs-config file
#-------------------------------------------------------------------------------
EXEC=$SCM_EXEC

# Set variables with defaults
PRINT_HELP=0
#-------------------------------------------------------------------------------
# Set SCM code executable path (EXEC) and an associated name (EXEC_NAME)
#-------------------------------------------------------------------------------
EXPT_NAME='ref-oifs-scm'

#-------------------------------------------------------------------------------
# Set default CASE_LIST and TIMESTEP_LIST, which can be overwritten if user 
# provides optional arguments, when executing callscm
# Both lists can be a single value or space-separated list to loop over
#-------------------------------------------------------------------------------
#CASE_LIST="TWPICE"
#CASE_LIST="BOMEX"
#CASE_LIST="DYCOMS"
CASE_LIST="DYCOMS BOMEX TWPICE"
#-------------------------------------------------------------------------------
# Set timestep (secs)
#-------------------------------------------------------------------------------
#TIMESTEP_LIST="1800 900 300"
TIMESTEP_LIST="450"

if [ "$#" -eq 0 ]; then
  log_echo "[INFO]: No arguments supplied so using defaults"
fi

#-------------------------------------------------------------------------------
# Check for user input to define case and/or timestep
#-------------------------------------------------------------------------------
while getopts hc:t:x: OPTION ; do
  case "${OPTION}" in 
    h) PRINT_HELP=1 ;;
    c) CASE_LIST="${OPTARG}" ;;
    t) TIMESTEP_LIST="${OPTARG}" ;;
    x) EXPT_NAME="${OPTARG}" ;;
    *) usage_info "invalid argument"
       exit 1 ;;
  esac
done

if [[ ${PRINT_HELP} == 1 ]] ; then
  usage_info "help requested"
  exit 1
fi

log_echo "[INFO]: Executable name = $EXEC"
log_echo "[INFO]: Experiment name = $EXPT_NAME"
log_echo "[INFO]: CASE_LIST= $CASE_LIST "
log_echo "[INFO]: TIMESTEP= $TIMESTEP_LIST "

#-------------------------------------------------------------------------
# Loop over SCM cases
#  For each case, need to specify:
#  SCM_IN: netcdf input file with initial profile and forcing
#  DURATION: Number of hours to run the simulation
#  There are also various plot attributes that can be set here
#  Other options for run.scm are available - see the run.scm script header 
#-------------------------------------------------------------------------
for CASE in ${CASE_LIST}
do
 
  #-------------------------------------
  # DYCOMS - stratocumulus
  #-------------------------------------
  if [ $CASE = 'DYCOMS' ] ; then
    SCM_IN=$CASESDIR/scm_in.DYCOMS_137.nc
    DURATION=h48 # hours

    for TIMESTEP in ${TIMESTEP_LIST}
    do
      "$SCM_TEST"/run.scm -X ${EXEC} -x ${EXPT_NAME} -t ${TIMESTEP}  -d ${DURATION} -c ${CASE} -s ${SCM_IN} | tee -a "$SCM_LOGFILE"
    done
  fi

  #-------------------------------------
  # Shallow convection: BOMEX
  #-------------------------------------
  if [ $CASE = 'BOMEX' ] ; then
    SCM_IN=scm_in.BOMEX_137.nc
    DURATION=h24 # hours (can be up to 96 hours)

    for TIMESTEP in ${TIMESTEP_LIST}
    do
      "$SCM_TEST"/run.scm -X ${EXEC} -x ${EXPT_NAME} -t ${TIMESTEP} -d ${DURATION} -c ${CASE} -s ${SCM_IN} | tee -a "$SCM_LOGFILE"
    done
  fi


  #-------------------------------------
  # Tropical deep convection: TWPICE
  #-------------------------------------
  if [ $CASE = 'TWPICE' ] ; then
    SCM_IN=scm_in.TWPICE_137.nc
    DURATION=h640 # hours
    for TIMESTEP in ${TIMESTEP_LIST}
    do
      "$SCM_TEST"/run.scm -X ${EXEC} -x ${EXPT_NAME} -t ${TIMESTEP} -d ${DURATION} -c ${CASE} -s ${SCM_IN} | tee -a "$SCM_LOGFILE"
    done
  fi

done # loop over SCM cases
 

exit
